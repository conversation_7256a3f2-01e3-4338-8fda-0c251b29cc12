#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify that vector chatbot uses generate_prompt_based_on_user_intent for feedback intents
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_prompt_based_feedback():
    """Test that vector chatbot uses generate_prompt_based_on_user_intent for feedback intents"""
    
    print("🎯 Testing Prompt-based Feedback in Vector Chatbot")
    print("=" * 60)
    
    try:
        from vector_chatbot.vector_chatbot_router import VectorChatbotRouter
        from new_response_generator import NewResponseGenerator
        
        # Initialize components
        print("🚀 Initializing components...")
        vector_router = VectorChatbotRouter()
        response_generator = NewResponseGenerator()
        print("✅ Components initialized successfully!\n")
        
        # Test cases for different feedback intents
        feedback_test_cases = [
            {
                "query": "What job opportunities do you have?",
                "expected_intent": "search_jobs",
                "description": "Job search query"
            },
            {
                "query": "Can you find jobs that match my CV skills?",
                "expected_intent": "search_jobs", 
                "description": "CV-based job search"
            },
            {
                "query": "What is the salary range for Python developers?",
                "expected_intent": "salary_query",
                "description": "Salary inquiry"
            },
            {
                "query": "Tell me about FOIS ICT PRO company",
                "expected_intent": "ask_company_info",
                "description": "Company information request"
            },
            {
                "query": "Can you review my CV and give feedback?",
                "expected_intent": "cv_feedback",
                "description": "CV feedback request"
            }
        ]
        
        print("🔍 Testing Feedback Intent Processing:")
        print("-" * 60)
        
        successful_tests = 0
        total_tests = len(feedback_test_cases)
        
        for i, test_case in enumerate(feedback_test_cases, 1):
            query = test_case["query"]
            expected_intent = test_case["expected_intent"]
            description = test_case["description"]
            
            print(f"\n📝 Test {i}: {description}")
            print(f"   Query: '{query}'")
            print(f"   Expected Intent: {expected_intent}")
            
            try:
                # Process the query through vector chatbot
                result = vector_router.process_user_input(query, "test_user", [])
                
                # Extract key information
                vector_intent = result.get('vector_intent', 'unknown')
                mapped_intent = result.get('mapped_intent', 'unknown')
                processing_method = result.get('processing_method', 'unknown')
                data_source = result.get('data_source', 'unknown')
                needs_feedback = result.get('needs_feedback', False)
                
                print(f"   🔍 Vector Intent: {vector_intent}")
                print(f"   🎯 Mapped Intent: {mapped_intent}")
                print(f"   🔄 Processing Method: {processing_method}")
                print(f"   📊 Data Source: {data_source}")
                print(f"   💭 Needs Feedback: {needs_feedback}")
                
                # Check if the intent mapping is correct
                intent_correct = mapped_intent == expected_intent
                
                # Check if it's using prompt-based response generation
                using_prompt_based = 'prompt_based' in processing_method or 'generate_prompt_based_on_user_intent' in data_source
                
                print(f"   ✅ Intent Mapping: {'✅ CORRECT' if intent_correct else '❌ INCORRECT'}")
                print(f"   🎨 Prompt-based: {'✅ YES' if using_prompt_based else '❌ NO'}")
                
                if intent_correct and using_prompt_based:
                    print(f"   🎉 TEST PASSED")
                    successful_tests += 1
                else:
                    print(f"   ❌ TEST FAILED")
                    if not intent_correct:
                        print(f"      - Expected intent: {expected_intent}, got: {mapped_intent}")
                    if not using_prompt_based:
                        print(f"      - Not using prompt-based response generation")
                
                # Test the specialized prompt generation directly
                print(f"   🧪 Testing Direct Prompt Generation:")
                try:
                    specialized_prompt = response_generator.generate_prompt_based_on_user_intent(
                        expected_intent, query, []
                    )
                    
                    prompt_length = len(specialized_prompt)
                    has_intent_specific_content = expected_intent.replace('_', ' ') in specialized_prompt.lower()
                    
                    print(f"      - Prompt Length: {prompt_length} characters")
                    print(f"      - Intent-specific Content: {'✅ YES' if has_intent_specific_content else '❌ NO'}")
                    
                    # Show a preview of the specialized prompt
                    preview = specialized_prompt[:200] + "..." if len(specialized_prompt) > 200 else specialized_prompt
                    print(f"      - Preview: {preview}")
                    
                except Exception as e:
                    print(f"      ❌ Error generating specialized prompt: {e}")
                
            except Exception as e:
                print(f"   ❌ Error processing query: {e}")
                import traceback
                traceback.print_exc()
        
        # Calculate success rate
        success_rate = (successful_tests / total_tests) * 100
        
        print(f"\n" + "=" * 60)
        print("📊 RESULTS:")
        print(f"✅ Successful Tests: {successful_tests}/{total_tests}")
        print(f"🎯 Success Rate: {success_rate:.1f}%")
        
        if success_rate >= 80:
            print("🎉 EXCELLENT: Vector chatbot is properly using prompt-based feedback!")
            return True
        elif success_rate >= 60:
            print("⚠️ GOOD: Vector chatbot mostly uses prompt-based feedback, minor issues")
            return False
        else:
            print("❌ POOR: Vector chatbot is not properly using prompt-based feedback")
            return False
        
    except Exception as e:
        print(f"❌ Critical error in prompt-based feedback test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_prompt_consistency():
    """Test that vector and regular chat use consistent prompts"""
    
    print("\n🔄 Testing Prompt Consistency Between Vector and Regular Chat")
    print("=" * 60)
    
    try:
        from new_response_generator import NewResponseGenerator
        
        response_generator = NewResponseGenerator()
        
        # Test intents that should have specialized prompts
        test_intents = [
            "search_jobs",
            "salary_query", 
            "ask_company_info",
            "cv_feedback"
        ]
        
        test_query = "Test query for prompt generation"
        
        print("🧪 Testing Specialized Prompt Generation:")
        
        for intent in test_intents:
            print(f"\n📝 Intent: {intent}")
            
            try:
                # Generate specialized prompt
                specialized_prompt = response_generator.generate_prompt_based_on_user_intent(
                    intent, test_query, []
                )
                
                # Analyze the prompt
                prompt_length = len(specialized_prompt)
                has_sample_jobs = 'SAMPLE_JOBS' in specialized_prompt
                has_intent_keywords = intent.replace('_', ' ') in specialized_prompt.lower()
                has_forbidden_section = 'ABSOLUTELY FORBIDDEN' in specialized_prompt
                has_must_do_section = 'MUST DO IMMEDIATELY' in specialized_prompt
                
                print(f"   📏 Length: {prompt_length} characters")
                print(f"   📊 Has Sample Jobs: {'✅' if has_sample_jobs else '❌'}")
                print(f"   🎯 Has Intent Keywords: {'✅' if has_intent_keywords else '❌'}")
                print(f"   🚫 Has Forbidden Section: {'✅' if has_forbidden_section else '❌'}")
                print(f"   ✅ Has Must Do Section: {'✅' if has_must_do_section else '❌'}")
                
                # Check if it's a specialized prompt (not just the default)
                is_specialized = has_sample_jobs or has_forbidden_section or prompt_length > 1000
                print(f"   🎨 Is Specialized: {'✅ YES' if is_specialized else '❌ NO'}")
                
            except Exception as e:
                print(f"   ❌ Error generating prompt for {intent}: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in prompt consistency test: {e}")
        return False


def main():
    """Run the prompt-based feedback tests"""
    print(f"🕒 Test started at: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test prompt-based feedback
    feedback_test_passed = test_prompt_based_feedback()
    
    # Test prompt consistency
    consistency_test_passed = test_prompt_consistency()
    
    print()
    print(f"🕒 Test completed at: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("\n" + "=" * 60)
    print("🎯 FINAL SUMMARY:")
    print(f"   Prompt-based Feedback Test: {'✅ PASSED' if feedback_test_passed else '❌ FAILED'}")
    print(f"   Prompt Consistency Test: {'✅ PASSED' if consistency_test_passed else '❌ FAILED'}")
    
    overall_success = feedback_test_passed and consistency_test_passed
    
    if overall_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Vector chatbot properly uses generate_prompt_based_on_user_intent")
        print("✅ Feedback intents get specialized prompts for better responses")
        print("✅ Consistent prompt generation between vector and regular chat")
    else:
        print("\n🔧 ISSUES FOUND:")
        if not feedback_test_passed:
            print("❌ Vector chatbot is not properly using prompt-based feedback")
        if not consistency_test_passed:
            print("❌ Prompt consistency issues between vector and regular chat")
        
        print("\n💡 Benefits of Using generate_prompt_based_on_user_intent:")
        print("1. ✅ Specialized prompts for different intent types")
        print("2. ✅ Better response quality and relevance")
        print("3. ✅ Consistent behavior between vector and regular chat")
        print("4. ✅ Intent-specific instructions and constraints")
        print("5. ✅ Improved user experience with targeted responses")
    
    return overall_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
