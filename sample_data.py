#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Dữ liệu mẫu cho các intent của EmailChatbot
"""

# Dữ liệu mẫu cho job opportunities
SAMPLE_JOBS = [
    {
        "id": "job_001",
        "title": "Senior Python Developer",
        "company": "FOIS ICT PRO",
        "location": "Hà Nội",
        "salary_range": "25-35 triệu VNĐ",
        "experience": "3-5 năm",
        "skills": ["Python", "Django", "FastAPI", "PostgreSQL", "Docker"],
        "type": "Full-time",
        "remote": True,
        "description": "Phát triển các ứng dụng web backend sử dụng Python, tham gia vào các dự án AI/ML của công ty.",
        "requirements": [
            "3+ năm kinh nghiệm Python",
            "Thành thạo Django hoặc FastAPI",
            "Kinh nghiệm với database (PostgreSQL, MongoDB)",
            "<PERSON><PERSON><PERSON> bi<PERSON><PERSON> về Docker, CI/CD"
        ]
    },
    {
        "id": "job_002",
        "title": "React Frontend Developer",
        "company": "FOIS ICT PRO",
        "location": "TP.HCM",
        "salary_range": "20-30 triệu VNĐ",
        "experience": "2-4 năm",
        "skills": ["React", "TypeScript", "Next.js", "Tailwind CSS", "Redux"],
        "type": "Full-time",
        "remote": True,
        "description": "Xây dựng giao diện người dùng hiện đại cho các ứng dụng web và mobile.",
        "requirements": [
            "2+ năm kinh nghiệm React",
            "Thành thạo TypeScript",
            "Kinh nghiệm với state management (Redux, Zustand)",
            "Hiểu biết về responsive design"
        ]
    },
    {
        "id": "job_004",
        "title": "Senior DevOps Engineer",
        "company": "FOIS ICT PRO",
        "location": "Remote",
        "salary_range": "40-60 triệu VNĐ",
        "experience": "4-7 năm",
        "skills": ["AWS", "Docker", "Kubernetes", "Terraform", "Jenkins", "GitOps", "Monitoring"],
        "type": "Full-time",
        "remote": True,
        "hot_tag": "☁️ CLOUD EXPERT",
        "description": "Quản lý infrastructure, CI/CD pipelines và monitoring cho các dự án cloud-native của công ty.",
        "requirements": [
            "4+ năm kinh nghiệm DevOps",
            "Thành thạo AWS/Azure/GCP",
            "Kinh nghiệm với Kubernetes production",
            "Hiểu biết về Infrastructure as Code và GitOps"
        ],
        "benefits": [
            "100% remote work",
            "Cloud certification support",
            "Latest DevOps tools",
            "Infrastructure ownership"
        ]
    },
    {
        "id": "job_004",
        "title": "AI/ML Engineer",
        "company": "FOIS ICT PRO",
        "location": "Hà Nội",
        "salary_range": "35-50 triệu VNĐ",
        "experience": "3-5 năm",
        "skills": ["Python", "TensorFlow", "PyTorch", "Scikit-learn", "MLOps"],
        "type": "Full-time",
        "remote": False,
        "description": "Phát triển và triển khai các mô hình AI/ML cho các dự án của khách hàng.",
        "requirements": [
            "3+ năm kinh nghiệm AI/ML",
            "Thành thạo Python, TensorFlow/PyTorch",
            "Kinh nghiệm với MLOps",
            "Hiểu biết về Deep Learning, NLP"
        ]
    },
    {
        "id": "job_005",
        "title": "Mobile Developer (React Native)",
        "company": "FOIS ICT PRO",
        "location": "TP.HCM",
        "salary_range": "22-32 triệu VNĐ",
        "experience": "2-4 năm",
        "skills": ["React Native", "JavaScript", "TypeScript", "Redux", "Firebase"],
        "type": "Full-time",
        "remote": True,
        "description": "Phát triển ứng dụng mobile cross-platform cho iOS và Android.",
        "requirements": [
            "2+ năm kinh nghiệm React Native",
            "Thành thạo JavaScript/TypeScript",
            "Kinh nghiệm publish app lên store",
            "Hiểu biết về native modules"
        ]
    },
    {
        "id": "job_006",
        "title": "Senior AI/ML Engineer",
        "company": "FOIS ICT PRO",
        "location": "Hà Nội/Remote",
        "salary_range": "45-65 triệu VNĐ",
        "experience": "3-5 năm",
        "skills": ["Python", "TensorFlow", "PyTorch", "MLOps", "LangChain", "OpenAI API"],
        "type": "Full-time",
        "remote": True,
        "hot_tag": "🔥 HOT TREND 2024",
        "description": "Phát triển các giải pháp AI/ML tiên tiến, xây dựng chatbot và automation systems.",
        "requirements": [
            "3+ năm kinh nghiệm AI/ML",
            "Thành thạo Python, TensorFlow/PyTorch",
            "Kinh nghiệm với LLM và NLP",
            "Hiểu biết về MLOps và deployment"
        ],
        "benefits": [
            "Mức lương top market",
            "Làm việc với công nghệ mới nhất",
            "Remote work flexible",
            "Training budget 20 triệu/năm"
        ]
    },
    {
        "id": "job_007",
        "title": "Senior Python Developer (AI Focus)",
        "company": "FOIS ICT PRO",
        "location": "TP.HCM/Remote",
        "salary_range": "35-50 triệu VNĐ",
        "experience": "4-6 năm",
        "skills": ["Python", "Django", "FastAPI", "AI/ML", "PostgreSQL", "Docker"],
        "type": "Full-time",
        "remote": True,
        "hot_tag": "🚀 PYTHON BOOM",
        "description": "Backend development với focus vào AI integration và scalable systems.",
        "requirements": [
            "4+ năm kinh nghiệm Python",
            "Thành thạo Django/FastAPI",
            "Kinh nghiệm tích hợp AI/ML models",
            "Hiểu biết về microservices"
        ],
        "benefits": [
            "Mức lương cạnh tranh",
            "Dự án AI cutting-edge",
            "Remote work support",
            "Career development path"
        ]
    },
    {
        "id": "job_008",
        "title": "Junior Backend Developer (Node.js)",
        "company": "FOIS ICT PRO",
        "location": "Đà Nẵng",
        "salary_range": "12-18 triệu VNĐ",
        "experience": "1-2 năm",
        "skills": ["Node.js", "Express", "MongoDB", "REST API"],
        "type": "Full-time",
        "remote": False,
        "description": "Hỗ trợ xây dựng backend API cho các hệ thống quản lý nội bộ và sản phẩm khách hàng.",
        "requirements": [
            "1+ năm kinh nghiệm Node.js",
            "Hiểu biết về MongoDB",
            "Kỹ năng debug và viết unit tests",
            "Khả năng làm việc nhóm tốt"
        ]
    },
    {
        "id": "job_009",
        "title": "UI/UX Designer",
        "company": "FOIS ICT PRO",
        "location": "TP.HCM",
        "salary_range": "18-28 triệu VNĐ",
        "experience": "2-3 năm",
        "skills": ["Figma", "Sketch", "UX Research", "Prototyping"],
        "type": "Full-time",
        "remote": False,
        "description": "Thiết kế giao diện người dùng thân thiện và hiện đại cho sản phẩm nội bộ và khách hàng.",
        "requirements": [
            "2+ năm kinh nghiệm thiết kế UI/UX",
            "Sử dụng thành thạo Figma",
            "Có portfolio các sản phẩm đã làm",
            "Hiểu biết về hành vi người dùng"
        ]
    },
    {
        "id": "job_010",
        "title": "Technical Project Manager",
        "company": "FOIS ICT PRO",
        "location": "Remote",
        "salary_range": "40-55 triệu VNĐ",
        "experience": "4-6 năm",
        "skills": ["Agile", "Scrum", "Jira", "Communication", "Software Lifecycle"],
        "type": "Full-time",
        "remote": True,
        "description": "Quản lý các dự án phần mềm từ đầu đến cuối, đảm bảo đúng tiến độ và chất lượng sản phẩm.",
        "requirements": [
            "4+ năm kinh nghiệm quản lý dự án CNTT",
            "Thành thạo Scrum, Agile",
            "Kỹ năng giao tiếp và điều phối nhóm tốt",
            "Hiểu biết về kỹ thuật phần mềm"
        ],
        "benefits": [
            "Làm việc từ xa hoàn toàn",
            "Bonus theo dự án",
            "Gói training kỹ năng quản lý",
            "Được tham gia định hướng sản phẩm"
        ]
    },
    {
        "id": "job_011",
        "title": "Data Engineer",
        "company": "FOIS ICT PRO",
        "location": "Hà Nội",
        "salary_range": "30-45 triệu VNĐ",
        "experience": "3-5 năm",
        "skills": ["Python", "ETL", "Airflow", "BigQuery", "SQL", "Data Warehouse"],
        "type": "Full-time",
        "remote": True,
        "description": "Xây dựng pipeline xử lý dữ liệu và tối ưu hệ thống phân tích cho các ứng dụng data-driven.",
        "requirements": [
            "3+ năm kinh nghiệm Data Engineering",
            "Kỹ năng viết ETL pipelines",
            "Thành thạo SQL và xử lý dữ liệu lớn",
            "Hiểu biết về data modeling"
        ]
    },
    {
        "id": "job_012",
        "title": "Quality Assurance Engineer",
        "company": "FOIS ICT PRO",
        "location": "TP.HCM",
        "salary_range": "15-25 triệu VNĐ",
        "experience": "2-3 năm",
        "skills": ["Manual Testing", "Automation", "Selenium", "Jest", "Test Case Management"],
        "type": "Full-time",
        "remote": False,
        "description": "Viết test case và thực hiện test sản phẩm web/mobile để đảm bảo chất lượng sản phẩm.",
        "requirements": [
            "2+ năm kinh nghiệm kiểm thử phần mềm",
            "Kinh nghiệm viết test cases và scripts",
            "Hiểu biết về CI/CD và testing lifecycle",
            "Khả năng phối hợp với team phát triển"
        ]
    },
    {
        "id": "job_013",
        "title": "Golang Backend Developer",
        "company": "FOIS ICT PRO",
        "location": "Hà Nội/Remote",
        "salary_range": "35-50 triệu VNĐ",
        "experience": "3-5 năm",
        "skills": ["Go", "gRPC", "Microservices", "Redis", "PostgreSQL"],
        "type": "Full-time",
        "remote": True,
        "hot_tag": "💡 SYSTEM SCALE",
        "description": "Phát triển các dịch vụ backend sử dụng Golang cho hệ thống phân tán hiệu năng cao.",
        "requirements": [
            "3+ năm kinh nghiệm Golang",
            "Kinh nghiệm xây dựng microservices",
            "Hiểu biết về caching và performance tuning",
            "Thành thạo gRPC hoặc REST"
        ]
    },
    {
        "id": "job_014",
        "title": "System Administrator (Linux)",
        "company": "FOIS ICT PRO",
        "location": "Đà Nẵng",
        "salary_range": "18-28 triệu VNĐ",
        "experience": "2-4 năm",
        "skills": ["Linux", "Bash", "Monitoring", "Security", "Networking"],
        "type": "Full-time",
        "remote": False,
        "description": "Quản trị hệ thống máy chủ Linux, giám sát bảo mật và vận hành ổn định.",
        "requirements": [
            "2+ năm kinh nghiệm quản trị Linux",
            "Kỹ năng scripting (Bash)",
            "Hiểu biết về bảo mật hệ thống",
            "Kinh nghiệm với công cụ monitoring (Zabbix, Prometheus)"
        ]
    },
    {
        "id": "job_015",
        "title": "Product Owner",
        "company": "FOIS ICT PRO",
        "location": "Hà Nội",
        "salary_range": "35-50 triệu VNĐ",
        "experience": "3-6 năm",
        "skills": ["Agile", "Product Strategy", "Stakeholder Management", "UI/UX", "Analytics"],
        "type": "Full-time",
        "remote": False,
        "description": "Xác định hướng phát triển sản phẩm, phối hợp với team kỹ thuật và thiết kế.",
        "requirements": [
            "Kinh nghiệm làm việc với product team",
            "Hiểu biết về thiết kế sản phẩm số",
            "Kỹ năng viết user story và phân tích yêu cầu",
            "Kỹ năng giao tiếp với stakeholder"
        ]
    },
    {
        "id": "job_016",
        "title": "Junior Frontend Developer",
        "company": "FOIS ICT PRO",
        "location": "TP.HCM",
        "salary_range": "12-18 triệu VNĐ",
        "experience": "1-2 năm",
        "skills": ["HTML", "CSS", "JavaScript", "React", "REST APIs"],
        "type": "Full-time",
        "remote": False,
        "description": "Tham gia xây dựng giao diện người dùng cho hệ thống quản lý nội bộ.",
        "requirements": [
            "Có kinh nghiệm với HTML/CSS/JS",
            "Biết React là lợi thế",
            "Tư duy logic và giao tiếp tốt",
            "Có tinh thần học hỏi cao"
        ]
    },
    {
        "id": "job_017",
        "title": "Cybersecurity Specialist",
        "company": "FOIS ICT PRO",
        "location": "Remote",
        "salary_range": "45-60 triệu VNĐ",
        "experience": "4-6 năm",
        "skills": ["Security Audit", "OWASP", "SIEM", "Penetration Testing", "Firewalls"],
        "type": "Full-time",
        "remote": True,
        "hot_tag": "🔐 SECURITY FIRST",
        "description": "Đảm bảo an toàn hệ thống ứng dụng, kiểm thử bảo mật và triển khai các giải pháp phòng chống xâm nhập.",
        "requirements": [
            "4+ năm kinh nghiệm bảo mật",
            "Thành thạo kiểm thử lỗ hổng bảo mật",
            "Hiểu biết sâu về network và web security",
            "Có chứng chỉ CEH/CISSP là lợi thế"
        ],
        "benefits": [
            "Thưởng theo kết quả kiểm thử",
            "Làm việc với các hệ thống quy mô lớn",
            "Tham gia các hội thảo bảo mật quốc tế"
        ]
    }
]

# Dữ liệu mẫu cho salary ranges (Cập nhật từ báo cáo ITviec 2024-2025)
SALARY_RANGES = {
    "junior_developer": {
        "range": "15-25 triệu VNĐ",
        "description": "Dành cho developer có 0-2 năm kinh nghiệm",
        "skills": ["HTML/CSS", "JavaScript", "Python/Java cơ bản"],
        "market_trend": "Ổn định, nhu cầu cao cho fresh graduates"
    },
    "mid_developer": {
        "range": "25-40 triệu VNĐ",
        "description": "Dành cho developer có 2-5 năm kinh nghiệm",
        "skills": ["Framework chuyên sâu", "Database", "API design"],
        "market_trend": "Nhu cầu rất cao, ưu tiên tuyển dụng"
    },
    "senior_developer": {
        "range": "40-60 triệu VNĐ",
        "description": "Dành cho developer có 5+ năm kinh nghiệm",
        "skills": ["Architecture design", "Team lead", "Mentoring"],
        "market_trend": "Khan hiếm, mức lương cạnh tranh cao"
    },
    "tech_lead": {
        "range": "60-80 triệu VNĐ",
        "description": "Dành cho tech lead có 7+ năm kinh nghiệm",
        "skills": ["Technical leadership", "Project management", "Strategic planning"],
        "market_trend": "Rất khan hiếm, package hấp dẫn"
    },
    "ai_ml_engineer": {
        "range": "35-60 triệu VNĐ",
        "description": "Dành cho AI/ML engineer có 3+ năm kinh nghiệm (Cập nhật 2024)",
        "skills": ["Machine Learning", "Deep Learning", "MLOps", "Data Science", "Python", "TensorFlow/PyTorch"],
        "market_trend": "HOT TREND 2024-2025! Mức lương cao nhất thị trường",
        "freelance_rate": "58.7 triệu VNĐ/tháng (cao nhất)",
        "experience_salary": {
            "3_years": "29.2 triệu VNĐ",
            "6_years": "42.3 triệu VNĐ"
        }
    },
    "python_developer": {
        "range": "25-50 triệu VNĐ",
        "description": "Python Developer - Ngôn ngữ hot nhất 2024",
        "skills": ["Python", "Django", "FastAPI", "Flask", "Data Science", "AI/ML"],
        "market_trend": "Nhu cầu cực cao, đặc biệt cho AI/ML projects",
        "experience_salary": {
            "6_years": "42.3 triệu VNĐ"
        }
    },
    "devops_engineer": {
        "range": "30-55 triệu VNĐ",
        "description": "Dành cho DevOps engineer có 3+ năm kinh nghiệm",
        "skills": ["Cloud platforms", "CI/CD", "Infrastructure", "Monitoring", "Kubernetes", "Docker"],
        "market_trend": "Nhu cầu tăng mạnh với digital transformation"
    },
    "blockchain_engineer": {
        "range": "40-70 triệu VNĐ",
        "description": "Blockchain Engineer - Công nghệ mới nổi",
        "skills": ["Blockchain", "Smart Contracts", "Web3", "Solidity", "Cryptocurrency"],
        "market_trend": "Thị trường mới, mức lương cao do khan hiếm nhân lực"
    }
}

# Dữ liệu mẫu cho platform usage
PLATFORM_FEATURES = {
    "job_search": {
        "title": "Tìm kiếm việc làm",
        "description": "Sử dụng bộ lọc để tìm job phù hợp với kỹ năng và kinh nghiệm",
        "steps": [
            "Truy cập trang tìm kiếm job",
            "Nhập từ khóa kỹ năng (Python, React, etc.)",
            "Chọn địa điểm và mức lương mong muốn",
            "Xem danh sách job phù hợp"
        ]
    },
    "profile_creation": {
        "title": "Tạo hồ sơ ứng viên",
        "description": "Tạo profile chuyên nghiệp để thu hút nhà tuyển dụng",
        "steps": [
            "Đăng ký tài khoản",
            "Upload CV và ảnh đại diện",
            "Điền thông tin kỹ năng và kinh nghiệm",
            "Thiết lập preferences về job"
        ]
    },
    "application_process": {
        "title": "Quy trình ứng tuyển",
        "description": "Hướng dẫn apply job hiệu quả",
        "steps": [
            "Tìm job phù hợp",
            "Đọc kỹ job description",
            "Customize CV cho từng vị trí",
            "Submit application và theo dõi status"
        ]
    }
}

# Dữ liệu mẫu cho feedback responses
FEEDBACK_RESPONSES = {
    "not_suitable": [
        "Cảm ơn feedback của bạn! Chúng tôi sẽ tìm những job phù hợp hơn.",
        "Hiểu rồi, job này có thể không match với expectation của bạn.",
        "Không sao, chúng tôi có nhiều job khác phù hợp hơn."
    ],
    "salary_too_low": [
        "Chúng tôi hiểu concern về mức lương. Có nhiều job với salary cao hơn.",
        "Noted về salary expectation, chúng tôi sẽ recommend job với mức lương phù hợp hơn."
    ],
    "location_issue": [
        "Chúng tôi có nhiều job remote hoặc ở location khác phù hợp hơn.",
        "Hiểu về vấn đề location, chúng tôi sẽ focus vào remote jobs."
    ]
}

# Dữ liệu mẫu cho follow-up contexts
FOLLOW_UP_CONTEXTS = {
    "job_discussion": {
        "keywords": ["job", "position", "vị trí", "công việc"],
        "responses": [
            "Về job mà chúng ta đang thảo luận...",
            "Quay lại vị trí công việc này...",
            "Tiếp tục về job opportunity..."
        ]
    },
    "salary_discussion": {
        "keywords": ["lương", "salary", "compensation", "thu nhập"],
        "responses": [
            "Về mức lương mà bạn quan tâm...",
            "Tiếp tục thảo luận về compensation...",
            "Quay lại vấn đề salary..."
        ]
    },
    "company_discussion": {
        "keywords": ["công ty", "company", "FOIS", "tổ chức"],
        "responses": [
            "Về FOIS ICT PRO mà chúng ta đang nói...",
            "Tiếp tục về thông tin công ty...",
            "Quay lại câu hỏi về organization..."
        ]
    }
}

# Dữ liệu mẫu cho not interested responses
NOT_INTERESTED_RESPONSES = [
    "Cảm ơn bạn đã dành thời gian! Chúng tôi sẽ lưu thông tin và liên hệ khi có cơ hội phù hợp hơn.",
    "Không sao cả! Nếu trong tương lai bạn có nhu cầu tìm việc, đừng ngần ngại liên hệ lại.",
    "Hiểu rồi! Chúng tôi sẽ không gửi thêm thông tin job. Chúc bạn thành công trong công việc hiện tại.",
    "Cảm ơn feedback! Chúng tôi sẽ respect quyết định của bạn và không disturb thêm."
]

# Dữ liệu mẫu cho other/general responses
OTHER_RESPONSES = [
    "Xin lỗi, tôi chưa hiểu rõ câu hỏi của bạn. Bạn có thể hỏi về job opportunities, company info, hoặc platform usage không?",
    "Câu hỏi này nằm ngoài chuyên môn của tôi. Tôi có thể giúp bạn về tuyển dụng, thông tin công ty, hoặc hướng dẫn sử dụng platform.",
    "Tôi là AI assistant chuyên về tuyển dụng. Bạn có muốn tìm hiểu về job opportunities hoặc thông tin FOIS ICT PRO không?"
]

# Job positions data for CV matching
JOB_POSITIONS = {
    "python_dev": {
        "title": "Python Developer",
        "company": "FOIS ICT PRO",
        "salary": "15-25 triệu VND",
        "location": "Hà Nội/HCM",
        "required_skills": ["python", "django", "flask", "sql", "git", "rest api"],
        "min_experience": 1,
        "max_experience": 5,
        "requirements": ["Python", "Django/Flask", "SQL", "Git", "REST API"],
        "description": "Phát triển ứng dụng web với Python, tham gia các dự án outsourcing cho khách hàng Nhật Bản"
    },
    "java_dev": {
        "title": "Java Developer",
        "company": "FOIS ICT PRO",
        "salary": "18-30 triệu VND",
        "location": "Hà Nội/HCM",
        "required_skills": ["java", "spring boot", "mysql", "git", "microservices"],
        "min_experience": 2,
        "max_experience": 6,
        "requirements": ["Java", "Spring Boot", "MySQL", "Git", "Microservices"],
        "description": "Phát triển ứng dụng enterprise với Java, làm việc với các dự án lớn từ Nhật Bản"
    },
    "frontend_dev": {
        "title": "Frontend Developer",
        "company": "FOIS ICT PRO",
        "salary": "12-22 triệu VND",
        "location": "Hà Nội/HCM",
        "required_skills": ["javascript", "react", "vue", "html", "css", "typescript"],
        "min_experience": 1,
        "max_experience": 4,
        "requirements": ["JavaScript", "React/Vue", "HTML/CSS", "TypeScript", "Git"],
        "description": "Phát triển giao diện người dùng hiện đại, responsive design"
    },
    "ai_engineer": {
        "title": "AI Engineer",
        "company": "FOIS ICT PRO",
        "salary": "25-40 triệu VND",
        "location": "Hà Nội/HCM",
        "required_skills": ["python", "tensorflow", "pytorch", "machine learning", "deep learning", "sql"],
        "min_experience": 2,
        "max_experience": 8,
        "requirements": ["Python", "TensorFlow/PyTorch", "Machine Learning", "Deep Learning", "SQL"],
        "description": "Phát triển các giải pháp AI/ML, nghiên cứu và ứng dụng công nghệ AI mới nhất"
    },
    "fullstack_dev": {
        "title": "Fullstack Developer",
        "company": "FOIS ICT PRO",
        "salary": "20-35 triệu VND",
        "location": "Hà Nội/HCM",
        "required_skills": ["javascript", "node.js", "react", "mongodb", "sql", "docker"],
        "min_experience": 2,
        "max_experience": 6,
        "requirements": ["JavaScript", "Node.js", "React", "MongoDB", "SQL", "Docker"],
        "description": "Phát triển ứng dụng full-stack, từ frontend đến backend và database"
    },
    "devops_engineer": {
        "title": "DevOps Engineer",
        "company": "FOIS ICT PRO",
        "salary": "22-38 triệu VND",
        "location": "Hà Nội/HCM",
        "required_skills": ["docker", "kubernetes", "aws", "jenkins", "linux", "terraform"],
        "min_experience": 2,
        "max_experience": 7,
        "requirements": ["Docker", "Kubernetes", "AWS", "Jenkins", "Linux", "Terraform"],
        "description": "Quản lý hạ tầng cloud, CI/CD, automation và monitoring"
    },
    "mobile_dev": {
        "title": "Mobile Developer",
        "company": "FOIS ICT PRO",
        "salary": "16-28 triệu VND",
        "location": "Hà Nội/HCM",
        "required_skills": ["react native", "flutter", "ios", "android", "javascript", "dart"],
        "min_experience": 1,
        "max_experience": 5,
        "requirements": ["React Native/Flutter", "iOS/Android", "JavaScript/Dart"],
        "description": "Phát triển ứng dụng mobile cross-platform cho iOS và Android"
    },
    "data_scientist": {
        "title": "Data Scientist",
        "company": "FOIS ICT PRO",
        "salary": "20-35 triệu VND",
        "location": "Hà Nội/HCM",
        "required_skills": ["python", "r", "sql", "pandas", "numpy", "scikit-learn", "tableau"],
        "min_experience": 1,
        "max_experience": 6,
        "requirements": ["Python/R", "SQL", "Pandas", "NumPy", "Scikit-learn", "Tableau"],
        "description": "Phân tích dữ liệu, xây dựng mô hình dự đoán và báo cáo insights"
    },
    "qa_engineer": {
        "title": "QA Engineer",
        "company": "FOIS ICT PRO",
        "salary": "12-20 triệu VND",
        "location": "Hà Nội/HCM",
        "required_skills": ["selenium", "automation testing", "manual testing", "sql", "jira"],
        "min_experience": 1,
        "max_experience": 4,
        "requirements": ["Selenium", "Automation Testing", "Manual Testing", "SQL", "JIRA"],
        "description": "Đảm bảo chất lượng sản phẩm thông qua testing manual và automation"
    },
    "ui_ux_designer": {
        "title": "UI/UX Designer",
        "company": "FOIS ICT PRO",
        "salary": "10-18 triệu VND",
        "location": "Hà Nội/HCM",
        "required_skills": ["figma", "sketch", "adobe xd", "photoshop", "user research", "prototyping"],
        "min_experience": 1,
        "max_experience": 4,
        "requirements": ["Figma/Sketch", "Adobe XD", "Photoshop", "User Research", "Prototyping"],
        "description": "Thiết kế giao diện và trải nghiệm người dùng cho các ứng dụng web/mobile"
    }
}

COMPANY_DETAILS = """"
FOIS đặt ra “Sáng tạo và Thách thức”, “Quản trị toàn bộ nhân viên” và “Quan điểm của khách hàng” là ba mục tiêu chính, tổng hợp các giá trị này và nỗ lực hàng ngày để mang lại hạnh phúc cho tất cả nhân viên và cung cấp dịch vụ tốt nhất để nhận sự hài lòng của khách hàng.
Chúng tôi đã được rất nhiều sự hỗ trợ và qua 33 năm kể từ khi thành lập vào năm 1991, chúng tôi vẫn tiếp tục phát triển và thêm nhiều nỗ lực hơn nữa cho tương lai.

Thông qua các trụ sở tại Nhật Bản (Tokyo, Nagoya) và Việt Nam (TP.HCM, Hà Nội), chúng tôi tập trung vào việc giải quyết thách thức và tạo ra giá trị bằng cách sử dụng công nghệ tiên tiến nhất. Trong một ngành công nghiệp ICT với sự tiến triển nhanh chóng của công nghệ như Trí tuệ nhân tạo (AI), IoT, chúng tôi tiến lên với sự cạnh tranh trên thị trường toàn cầu và thành lập FOIS ICT PRO tại Việt Nam và Nhật Bản. Điều này đánh dấu sự khởi đầu cho một thách thức mới, với mục tiêu là “Cung cấp dịch vụ ICT từ Việt Nam và Nhật Bản ra thế giới” .”Trở thành một công ty thương mại chuyên nghiệp” dưới góc nhìn của khách hàng.

Tất cả nhân viên của FOIS ICT PRO, từ Nhật Bản đến Việt Nam và các quốc gia trên thế giới, tự hào về sự hợp tác với các doanh nghiệp và tiếp tục thách thức hướng tới tương lai. Mục tiêu của chúng tôi là tạo ra giá trị thông qua sự phát triển bền vững và các phương pháp tiếp cận sáng tạo.

Chúng tôi rất mong nhận được sự ủng hộ và tin tưởng từ quý vị trong tương lai. Xin chân thành cảm ơn.

Đội ngũ:
Cuong

Lập trình viên
Từ năm 2012
Chúng ta hãy cùng nhau đón nhận những thử thách mới, cùng nhau xây dựng các dự án và cùng nhau phát triển sự nghiệp.

Thanh

Thiết kế
Từ năm 2013
Chúng tôi có một môi trường rất thân thiện và không ngừng hỗ trợ nhân viên nâng cao kỹ năng cũng như đào tạo thông qua các dự án. Chúng tôi lắng nghe cẩn thận ý tưởng của nhân viên và cố gắng cùng phát triển với họ.

Bao
Lập trình viên
Từ năm 2014
Công ty có nhiều cơ hội phát triển bản thân.

Nhat
Nhân viên Sales

Công ty đang cần tuyển dụng:
Người không sợ thay đổi,
thích thú với thách thức
「Chính trị」「Kinh tế」「Công nghệ」 Môi trường xung quanh chúng ta đang thay đổi nhanh chóng. Đặc biệt, ngành công nghệ thông tin (IT) ngày càng tiến bộ với các mô hình kinh doanh và công nghệ mới được phát triển mỗi năm, và lĩnh vực này cũng thay đổi đột ngột đến mức các công nghệ phổ biến hiện nay có thể trở nên lỗi thời trong thời gian ngắn. Mọi người đều có thể cảm thấy bất an trước sự thay đổi, nhưng đồng thời cũng đang có nhu cầu tuyển dụng nhân tài có khả năng linh hoạt đối phó với sự thay đổi, trong khi vẫn tìm kiếm sự ổn định.

Tại FOIS ICT PRO, chúng tôi coi trọng thái độ tích cực đương đầu sự thay đổi và thích thú với thách thức. Trong khi tìm kiếm sự ổn định, việc thích nghi và tiếp tục học hỏi về môi trường, kinh doanh và công nghệ mới là rất quan trọng. Chúng tôi đang chờ đợi những cá nhân tích cực không sợ thay đổi, và thích thú với việc thách thức và chủ động trong việc chấp nhận những thay đổi.

Người có ước mơ và
nhiệt huyết để làm việc
Để hành động, con người cần có động lực. Tại trung tâm của động lực này là những ước mơ và mục tiêu cá nhân. Trong công việc, không chỉ có những khoảnh khắc vui vẻ mà còn có những tình huống khó khăn và rào cản lớn. Để đối mặt và vượt qua những thách thức này, việc có những ước mơ và mục tiêu lớn trong cuộc sống và dành sự đam mê cho việc thực hiện chúng là vô cùng quan trọng.

Trong xã hội hiện đại, việc theo đuổi ước mơ trở nên khó khăn hơn, nhưng tại FOIS ICT PRO, chúng tôi đang chờ đợi những cá nhân có ước mơ lớn và đam mê, có khả năng làm việc cùng đồng đội để thực hiện chúng. Chúng tôi rất mong được gặp gỡ những người bạn có thể cùng nhau theo đuổi ước mơ và phát triển cùng nhau tại FOIS ICT PRO

Người quan trọng và có thể làm cho người khác cười
FOIS ICT PRO đặt mục tiêu trở thành một “công ty đóng góp vào sự tiến bộ của xã hội bằng cách tạo ra tương lai, cảm xúc và nụ cười cho mọi người thông qua việc phát triển sản phẩm sử dụng công nghệ thông tin và truyền thông (ICT)”. Chúng tôi mang theo niềm tin rằng “Sản xuất hàng hóa là việc tạo ra con người”, và chúng tôi tìm kiếm những người có khả năng quan trọng hóa cả khách hàng, gia đình, bạn bè và đồng nghiệp trong công ty, và làm cho họ cười.

"""

# ===== MARKET INSIGHTS 2024-2025 (Từ báo cáo ITviec, TopDev, Navigos) =====

# Xu hướng thị trường IT Việt Nam 2024-2025
IT_MARKET_TRENDS_2024_2025 = {
    "overview": {
        "status": "Tăng trưởng mạnh bất chấp khó khăn kinh tế toàn cầu",
        "key_drivers": [
            "Chuyển đổi số (Digital Transformation)",
            "Điện toán đám mây (Cloud Computing)",
            "Trí tuệ nhân tạo (AI)",
            "Đầu tư nước ngoài liên tục",
            "Việt Nam trở thành trung tâm công nghệ ASEAN"
        ],
        "challenges": [
            "Thiếu hụt nhân lực có kinh nghiệm",
            "Cạnh tranh gay gắt về lương",
            "Nâng cấp cơ sở hạ tầng",
            "Đáp ứng tiêu chuẩn toàn cầu"
        ]
    },

    "hot_technologies_2024": {
        "ai_ml": {
            "demand": "Cực cao",
            "salary_growth": "+25-30%",
            "description": "AI/ML dẫn đầu về mức lương và nhu cầu tuyển dụng"
        },
        "python": {
            "demand": "Rất cao",
            "salary_growth": "+20%",
            "description": "Ngôn ngữ phổ biến nhất cho AI/ML và backend development"
        },
        "cloud_technologies": {
            "demand": "Cao",
            "salary_growth": "+15-20%",
            "description": "AWS, Azure, GCP - Nhu cầu tăng mạnh với digital transformation"
        },
        "blockchain": {
            "demand": "Trung bình nhưng lương cao",
            "salary_growth": "+30%",
            "description": "Thị trường mới, khan hiếm nhân lực có kinh nghiệm"
        }
    },

    "recruitment_trends": {
        "experience_priority": "Ưu tiên tuyển dụng nhân viên có kinh nghiệm",
        "fresh_graduate_challenge": "Khó khăn hơn cho fresh graduates",
        "training_programs": "Tăng cường chương trình đào tạo nội bộ",
        "remote_work": "Xu hướng làm việc từ xa tiếp tục phát triển",
        "skill_focus": "Tập trung vào kỹ năng thực tế và kinh nghiệm dự án"
    },

    "salary_insights": {
        "top_paying_sectors": [
            "AI, Blockchain, Deep Tech (61.5 triệu VNĐ/tháng)",
            "Nông nghiệp (67.8 triệu VNĐ/tháng)",
            "Bất động sản & Xây dựng (57.3 triệu VNĐ/tháng)"
        ],
        "freelance_rates": {
            "ai_ml_projects": "58.7 triệu VNĐ/tháng",
            "description": "Dự án AI/ML có thu nhập cao nhất cho freelancer"
        },
        "growth_expectation": "15-25% tăng lương khi chuyển việc"
    }
}

# Dữ liệu về các công nghệ hot trend 2024-2025
HOT_TECH_STACK_2024 = {
    "frontend": {
        "trending": ["React", "Next.js", "Vue 3", "TypeScript", "Tailwind CSS"],
        "salary_range": "20-45 triệu VNĐ",
        "demand": "Cao",
        "note": "TypeScript và modern frameworks đang thống trị"
    },
    "backend": {
        "trending": ["Python", "Node.js", "Go", "Java Spring Boot", "Microservices"],
        "salary_range": "25-55 triệu VNĐ",
        "demand": "Rất cao",
        "note": "Python dẫn đầu nhờ AI/ML boom"
    },
    "ai_ml": {
        "trending": ["Python", "TensorFlow", "PyTorch", "LangChain", "OpenAI API", "MLOps"],
        "salary_range": "35-70 triệu VNĐ",
        "demand": "Cực cao",
        "note": "Mức lương cao nhất thị trường, nhu cầu tăng vọt"
    },
    "cloud_devops": {
        "trending": ["AWS", "Azure", "GCP", "Kubernetes", "Docker", "Terraform"],
        "salary_range": "30-60 triệu VNĐ",
        "demand": "Cao",
        "note": "Digital transformation thúc đẩy nhu cầu"
    },
    "mobile": {
        "trending": ["React Native", "Flutter", "Swift", "Kotlin"],
        "salary_range": "22-40 triệu VNĐ",
        "demand": "Ổn định",
        "note": "Cross-platform development được ưa chuộng"
    },
    "blockchain": {
        "trending": ["Solidity", "Web3.js", "Smart Contracts", "DeFi", "NFT"],
        "salary_range": "40-80 triệu VNĐ",
        "demand": "Trung bình nhưng lương cao",
        "note": "Thị trường mới, khan hiếm nhân lực"
    }
}

# Thông tin về thị trường freelance IT 2024
FREELANCE_MARKET_2024 = {
    "top_earning_categories": {
        "ai_ml_development": {
            "avg_monthly": "58.7 triệu VNĐ",
            "description": "Phát triển AI/ML, chatbot, automation"
        },
        "web_development": {
            "avg_monthly": "35-45 triệu VNĐ",
            "description": "Full-stack development, e-commerce"
        },
        "mobile_development": {
            "avg_monthly": "30-40 triệu VNĐ",
            "description": "iOS, Android, React Native apps"
        },
        "devops_consulting": {
            "avg_monthly": "40-50 triệu VNĐ",
            "description": "Cloud migration, CI/CD setup"
        }
    },
    "trends": [
        "Tăng cường nhu cầu về AI/ML freelance projects",
        "Remote work trở thành chuẩn mực",
        "Khách hàng quốc tế tăng mạnh",
        "Yêu cầu kỹ năng chuyên sâu cao hơn"
    ]
}

# Dự báo thị trường IT 2025
IT_FORECAST_2025 = {
    "growth_sectors": [
        "AI & Machine Learning",
        "Cloud Computing & DevOps",
        "Cybersecurity",
        "IoT & Edge Computing",
        "Blockchain & Web3"
    ],
    "skill_demands": [
        "AI/ML Engineering",
        "Cloud Architecture",
        "Full-stack Development",
        "Data Science & Analytics",
        "Cybersecurity"
    ],
    "salary_predictions": {
        "ai_ml_engineer": "40-80 triệu VNĐ",
        "senior_python_dev": "45-65 triệu VNĐ",
        "cloud_architect": "50-75 triệu VNĐ",
        "blockchain_dev": "45-85 triệu VNĐ"
    },
    "market_outlook": "Tiếp tục tăng trưởng mạnh, Việt Nam củng cố vị thế trung tâm công nghệ ASEAN"
}

# Thông tin chi tiết về các vị trí hot 2024-2025
JOB_MARKET_INSIGHTS_2024 = {
    "most_demanded_positions": [
        {
            "title": "AI/ML Engineer",
            "demand_level": "Cực cao",
            "avg_salary": "35-60 triệu VNĐ",
            "key_skills": ["Python", "TensorFlow", "PyTorch", "MLOps"],
            "market_note": "Vị trí hot nhất 2024, mức lương tăng 25-30%"
        },
        {
            "title": "Python Developer",
            "demand_level": "Rất cao",
            "avg_salary": "25-50 triệu VNĐ",
            "key_skills": ["Python", "Django", "FastAPI", "AI/ML"],
            "market_note": "Ngôn ngữ phổ biến nhất, đặc biệt cho AI projects"
        },
        {
            "title": "Cloud Engineer/DevOps",
            "demand_level": "Cao",
            "avg_salary": "30-55 triệu VNĐ",
            "key_skills": ["AWS", "Azure", "Kubernetes", "Docker"],
            "market_note": "Digital transformation thúc đẩy nhu cầu"
        },
        {
            "title": "Full-stack Developer",
            "demand_level": "Cao",
            "avg_salary": "25-45 triệu VNĐ",
            "key_skills": ["React", "Node.js", "TypeScript", "MongoDB"],
            "market_note": "Vẫn là backbone của industry"
        },
        {
            "title": "Blockchain Developer",
            "demand_level": "Trung bình",
            "avg_salary": "40-70 triệu VNĐ",
            "key_skills": ["Solidity", "Web3", "Smart Contracts"],
            "market_note": "Thị trường mới, lương cao do khan hiếm"
        }
    ],

    "hiring_challenges": [
        "Thiếu hụt nhân lực có kinh nghiệm",
        "Cạnh tranh gay gắt về package lương",
        "Fresh graduates thiếu kỹ năng thực tế",
        "Yêu cầu kỹ năng ngày càng cao"
    ],

    "candidate_expectations": [
        "Remote work flexibility",
        "Competitive salary (15-25% tăng khi chuyển việc)",
        "Learning & development opportunities",
        "Modern tech stack",
        "Work-life balance"
    ]
}
