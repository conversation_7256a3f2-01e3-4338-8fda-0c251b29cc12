import streamlit as st
from google import generativeai as genai
import numpy as np
from sklearn.metrics.pairwise import cosine_similarity

# Cấu hình API key
genai.configure(api_key="AIzaSyB8JY1uWV_pwGoIGQMJ0n5GTrNuLcAOjIc")

# Hàm gọi Gemini để lấy embedding


def get_embedding(text):
    response = genai.embed_content(
        model="models/text-embedding-004",
        content=text,
        task_type="RETRIEVAL_DOCUMENT"
    )
    return np.array(response['embedding'])

# Hàm cosine similarity


def cosine_sim(vec1, vec2):
    return cosine_similarity([vec1], [vec2])[0][0]

# Chuẩn hóa thông tin


def prepare_text(data):
    return {
        'category': data['category'],
        'title': data['title'],
        'content': data['content'],
        'skills': ', '.join(data['skills']),
        'language': data['language'],
        'location': data['location']
    }


st.title("🔍 So s<PERSON>h mức độ tương đồng giữa hai công việc (AI Embedding)")
col1, col2 = st.columns(2)
with col1:
    st.header("🧩 Recruit")
    job1 = {
        'category': st.text_input("Category", "Backend Developer"),
        'title': st.text_input("Title", "Kỹ sư backend Python"),
        'content': st.text_area("Content", "Phát triển hệ thống REST API, làm việc với PostgreSQL, Docker."),
        'skills': st.text_input("Skills (phân cách bởi dấu phẩy)", "Python, PostgreSQL, Docker").split(","),
        'language': st.text_input("Language", "Japanese N3"),
        'location': st.text_input("Location", "Tokyo")
    }


with col2:
    st.header("🎯 Engineer")
    job2 = {
        'category': st.text_input("Category", "Backend Developer", key="engineer_category"),
        'title': st.text_input("Role", "Kỹ sư backend Python", key="engineer_title"),
        'content': st.text_area("Professional Summary", "Phát triển hệ thống REST API, làm việc với PostgreSQL, Docker.", key="engineer_content"),
        'skills': st.text_input("Skills (phân cách bởi dấu phẩy)", "Python, PostgreSQL, Docker", key="engineer_skills").split(","),
        'language': st.text_input("Language", "Japanese N3", key="engineer_language"),
        'location': st.text_input("Location", "Hà Nội", key="engineer_location")
    }

st.header("⚖️ Trọng số cho từng phần")
weights = {
    'category': st.slider("Weight - Category", 0.0, 1.0, 0.5),
    'title': st.slider("Weight - Title", 0.0, 1.0, 0.15),
    'content': st.slider("Weight - Content", 0.0, 1.0, 0.15),
    'skills': st.slider("Weight - Skills", 0.0, 1.0, 0.15),
    'language': st.slider("Weight - Language", 0.0, 1.0, 0.15),
    'location': st.slider("Weight - Location", 0.0, 1.0, 0.15),
}

# Normalize weights if tổng > 1
total_weight = sum(weights.values())
if total_weight > 1:
    weights = {k: v / total_weight for k, v in weights.items()}
    # st.warning("⚠️ Tổng trọng số vượt quá 1. Đã tự động chuẩn hóa.")

if st.button("🔎 Tính điểm tương đồng"):
    with st.spinner("Đang tính toán embedding..."):
        job1_parts = prepare_text(job1)
        job2_parts = prepare_text(job2)

        job1_vecs = {k: get_embedding(v) for k, v in job1_parts.items()}
        job2_vecs = {k: get_embedding(v) for k, v in job2_parts.items()}

        # Tính điểm từng phần và tổng hợp
        final_score = 0.0
        st.subheader("📊 Chi tiết điểm theo từng mục")
        for key in weights:
            sim_score = cosine_sim(job1_vecs[key], job2_vecs[key])
            weighted_score = weights[key] * sim_score
            final_score += weighted_score

            st.write(
                f"**{key.capitalize()}**: similarity = `{sim_score:.4f}`, "
                f"weight = `{weights[key]:.2f}`, "
                f"weighted = `{weighted_score:.4f}`"
            )

        st.success(f"✅ Điểm tương đồng có trọng số: **{final_score:.4f}**")
