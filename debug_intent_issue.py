#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug script to identify the intent classification issue
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_intent_classification():
    """Debug the specific intent classification issue"""
    
    print("🔍 Debugging Intent Classification Issue")
    print("=" * 50)
    
    try:
        from vector_intent_detector import VectorIntentDetector
        from vector_chatbot.vector_chatbot_router import VectorChatbotRouter
        
        # Initialize components
        print("🚀 Initializing components...")
        vector_detector = VectorIntentDetector()
        vector_router = VectorChatbotRouter()
        print("✅ Components initialized successfully!\n")
        
        # The specific problematic query
        problematic_query = "Can you find jobs that match my CV skills?"
        
        print(f"🎯 Testing Problematic Query:")
        print(f"📝 Query: '{problematic_query}'")
        print("-" * 50)
        
        # Step 1: Test vector intent detection
        print("🔍 Step 1: Vector Intent Detection")
        vector_intent, confidence, similarities = vector_detector.get_best_intent(problematic_query)
        print(f"   Vector Intent: {vector_intent}")
        print(f"   Confidence: {confidence:.3f}")
        
        # Show all similarities
        print(f"   All Similarities:")
        sorted_similarities = sorted(similarities.items(), key=lambda x: x[1], reverse=True)
        for intent, sim in sorted_similarities:
            print(f"      {intent}: {sim:.3f}")
        
        # Step 2: Test intent mapping
        print(f"\n🔄 Step 2: Intent Mapping")
        mapped_intent = vector_router.intent_integration.intent_mapping.get(vector_intent, vector_intent)
        print(f"   Original Intent: {vector_intent}")
        print(f"   Mapped Intent: {mapped_intent}")
        
        # Step 3: Test full router processing
        print(f"\n🎨 Step 3: Full Router Processing")
        router_result = vector_router.process_user_input(problematic_query, "test_user", [])
        
        print(f"   Final Result:")
        print(f"      Vector Intent: {router_result.get('vector_intent', 'unknown')}")
        print(f"      Mapped Intent: {router_result.get('mapped_intent', 'unknown')}")
        print(f"      Confidence: {router_result.get('confidence', 0):.3f}")
        print(f"      Needs Feedback: {router_result.get('needs_feedback', False)}")
        print(f"      Processing Method: {router_result.get('processing_method', 'unknown')}")
        
        # Step 4: Check what the expected result should be
        print(f"\n✅ Step 4: Expected vs Actual")
        expected_intent = "search_jobs"
        actual_intent = router_result.get('mapped_intent', 'unknown')
        
        print(f"   Expected Intent: {expected_intent}")
        print(f"   Actual Intent: {actual_intent}")
        
        if actual_intent == expected_intent:
            print(f"   ✅ CORRECT: Intent classification is working!")
            return True
        else:
            print(f"   ❌ INCORRECT: Intent classification needs fixing!")
            
            # Analyze why it's wrong
            print(f"\n🔧 Analysis:")
            
            # Check if search_jobs has higher similarity than the chosen intent
            search_jobs_sim = similarities.get('search_jobs', 0)
            chosen_sim = similarities.get(vector_intent, 0)
            
            print(f"   search_jobs similarity: {search_jobs_sim:.3f}")
            print(f"   {vector_intent} similarity: {chosen_sim:.3f}")
            
            if search_jobs_sim > chosen_sim:
                print(f"   🔍 Issue: search_jobs has higher similarity but wasn't chosen!")
                print(f"   💡 Possible cause: Bug in get_best_intent() method")
            elif search_jobs_sim < chosen_sim:
                print(f"   🔍 Issue: search_jobs has lower similarity")
                print(f"   💡 Possible cause: Intent descriptions need improvement")
            else:
                print(f"   🔍 Issue: Equal similarities")
                print(f"   💡 Possible cause: Tie-breaking logic issue")
            
            return False
        
    except Exception as e:
        print(f"❌ Critical error in debug: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_multiple_cv_queries():
    """Test multiple CV-based job search queries"""
    
    print("\n" + "🧪 Testing Multiple CV-based Queries")
    print("=" * 50)
    
    try:
        from vector_intent_detector import VectorIntentDetector
        
        vector_detector = VectorIntentDetector()
        
        cv_queries = [
            "Can you find jobs that match my CV skills?",
            "Show me positions suitable for my background",
            "What jobs are available for someone with my experience?",
            "Find me jobs that fit my profile",
            "I want to see job opportunities that match my CV"
        ]
        
        correct_count = 0
        
        for i, query in enumerate(cv_queries, 1):
            print(f"\n📝 Query {i}: {query}")
            
            vector_intent, confidence, similarities = vector_detector.get_best_intent(query)
            
            print(f"   Result: {vector_intent} (confidence: {confidence:.3f})")
            
            if vector_intent == 'search_jobs':
                print(f"   ✅ CORRECT")
                correct_count += 1
            else:
                print(f"   ❌ INCORRECT (expected: search_jobs)")
                
                # Show top 3 for debugging
                top_3 = sorted(similarities.items(), key=lambda x: x[1], reverse=True)[:3]
                print(f"   Top 3:")
                for intent, sim in top_3:
                    print(f"      {intent}: {sim:.3f}")
        
        accuracy = (correct_count / len(cv_queries)) * 100
        print(f"\n📊 Results: {correct_count}/{len(cv_queries)} correct ({accuracy:.1f}%)")
        
        return accuracy >= 80
        
    except Exception as e:
        print(f"❌ Error in multiple query test: {e}")
        return False


def main():
    """Run the debug tests"""
    print(f"🕒 Debug started at: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test the specific problematic case
    single_test_passed = debug_intent_classification()
    
    # Test multiple CV queries
    multiple_test_passed = test_multiple_cv_queries()
    
    print()
    print(f"🕒 Debug completed at: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("\n" + "=" * 50)
    print("🎯 SUMMARY:")
    print(f"   Single Query Test: {'✅ PASSED' if single_test_passed else '❌ FAILED'}")
    print(f"   Multiple Query Test: {'✅ PASSED' if multiple_test_passed else '❌ FAILED'}")
    
    if single_test_passed and multiple_test_passed:
        print("\n🎉 All tests passed! Intent classification is working correctly.")
    else:
        print("\n🔧 Issues found. Recommendations:")
        print("1. Enhance 'search_jobs' intent descriptions with more CV-related keywords")
        print("2. Check vector similarity calculation logic")
        print("3. Verify intent mapping in vector_chatbot_router.py")
        print("4. Test with updated intent descriptions")
    
    return single_test_passed and multiple_test_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
