#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive Test Script for Vector Chatbot Flow
Tests all 6 steps of the vector chatbot process with 10 strategic questions
"""

import sys
import os
import time
from datetime import datetime

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_vector_chatbot_flow():
    """Test the complete 6-step vector chatbot flow with 10 strategic questions"""
    
    print("🚀 Vector Chatbot Flow Test Suite")
    print("=" * 60)
    print("Testing the complete 6-step process:")
    print("1. Receive user input")
    print("2. Calculate user input vector") 
    print("3. Calculate similarities between user input vector and user intents")
    print("4. Find the user_intent with the highest score")
    print("5. If user_intent needs feedback -> call feedback function")
    print("6. Otherwise -> call new_response_generator_vector")
    print("=" * 60)
    print()
    
    # Test questions covering all major flows
    test_questions = [
        {
            "id": 1,
            "question": "Hello! Good morning, how are you today?",
            "expected_intent": "GREETINGS",
            "expected_flow": "Step 6 (new_response_generator_vector)",
            "test_focus": "Basic greeting - non-feedback intent",
            "category": "General"
        },
        {
            "id": 2,
            "question": "Tell me about blockchain developer jobs and salary ranges",
            "expected_intent": "ASK_JOB_DETAILS",
            "expected_flow": "Step 5 (feedback function - job_feedback)",
            "test_focus": "Job search with specific technology (blockchain)",
            "category": "Job Search"
        },
        {
            "id": 3,
            "question": "What's the salary for AI/ML Engineer positions in Vietnam?",
            "expected_intent": "SALARY_EXPECTATION",
            "expected_flow": "Step 5 (feedback function - salary_feedback)",
            "test_focus": "Salary inquiry for specific role",
            "category": "Salary"
        },
        {
            "id": 4,
            "question": "Can you review my CV and give me feedback on how to improve it?",
            "expected_intent": "SHARE_PROFILE",
            "expected_flow": "Step 5 (feedback function - cv_feedback)",
            "test_focus": "CV review and improvement advice",
            "category": "CV Analysis"
        },
        {
            "id": 5,
            "question": "What job opportunities do you have for Python developers?",
            "expected_intent": "ASK_JOB_OPPORTUNITIES",
            "expected_flow": "Step 5 (feedback function - job_feedback)",
            "test_focus": "Job opportunities for specific skill",
            "category": "Job Search"
        },
        {
            "id": 6,
            "question": "Tell me about FOIS ICT PRO company and what services you provide",
            "expected_intent": "ASK_COMPANY_INFO",
            "expected_flow": "Step 6 (new_response_generator_vector)",
            "test_focus": "Company information request",
            "category": "Company Info"
        },
        {
            "id": 7,
            "question": "How do I use this platform? What features are available?",
            "expected_intent": "ASK_PLATFORM_USAGE",
            "expected_flow": "Step 6 (new_response_generator_vector)",
            "test_focus": "Platform usage guidance",
            "category": "Platform Help"
        },
        {
            "id": 8,
            "question": "What are the requirements for Senior DevOps Engineer position?",
            "expected_intent": "ASK_JOB_DETAILS",
            "expected_flow": "Step 5 (feedback function - job_feedback)",
            "test_focus": "Specific job requirements inquiry",
            "category": "Job Details"
        },
        {
            "id": 9,
            "question": "I'm interested in remote work opportunities in cloud computing",
            "expected_intent": "ASK_JOB_OPPORTUNITIES",
            "expected_flow": "Step 5 (feedback function - job_feedback)",
            "test_focus": "Remote work and technology-specific search",
            "category": "Job Search"
        },
        {
            "id": 10,
            "question": "What's the weather like today? Can you help me with cooking recipes?",
            "expected_intent": "OTHER",
            "expected_flow": "Step 6 (new_response_generator_vector)",
            "test_focus": "Out-of-scope questions (non-career related)",
            "category": "Other"
        }
    ]
    
    try:
        from vector_chatbot.vector_chatbot_router import VectorChatbotRouter
        
        # Initialize the router
        print("🔧 Initializing Vector Chatbot Router...")
        router = VectorChatbotRouter()
        print("✅ Router initialized successfully!\n")
        
        # Test each question
        results = []
        for test_case in test_questions:
            print(f"🧪 Test {test_case['id']}: {test_case['category']}")
            print(f"📝 Question: \"{test_case['question']}\"")
            print(f"🎯 Expected Intent: {test_case['expected_intent']}")
            print(f"🔄 Expected Flow: {test_case['expected_flow']}")
            print(f"💡 Test Focus: {test_case['test_focus']}")
            print("-" * 50)
            
            start_time = time.time()
            
            try:
                # Process the question through the vector chatbot
                response = router.process_user_input(
                    user_input=test_case['question'],
                    user_id=f"test_user_{test_case['id']}",
                    conversation_history=[]
                )
                
                end_time = time.time()
                processing_time = end_time - start_time
                
                # Analyze the response
                detected_intent = response.get('vector_intent', 'UNKNOWN')
                response_type = response.get('response_type', 'unknown')
                message_length = len(response.get('message', ''))
                processing_method = response.get('processing_method', 'unknown')
                
                # Determine if it went through feedback or regular flow
                actual_flow = "Step 5 (feedback function)" if processing_method == "feedback_handler" else "Step 6 (new_response_generator_vector)"
                
                # Check if intent matches expectation
                intent_match = detected_intent == test_case['expected_intent']
                flow_match = "feedback" in actual_flow.lower() and "feedback" in test_case['expected_flow'].lower()
                
                # Store results
                test_result = {
                    'test_id': test_case['id'],
                    'question': test_case['question'],
                    'expected_intent': test_case['expected_intent'],
                    'detected_intent': detected_intent,
                    'intent_match': intent_match,
                    'expected_flow': test_case['expected_flow'],
                    'actual_flow': actual_flow,
                    'flow_match': flow_match,
                    'response_type': response_type,
                    'message_length': message_length,
                    'processing_time': processing_time,
                    'success': intent_match and (flow_match or test_case['expected_intent'] == 'OTHER'),
                    'category': test_case['category']
                }
                results.append(test_result)
                
                # Print results
                print(f"📊 Results:")
                print(f"   ✅ Detected Intent: {detected_intent} {'✓' if intent_match else '✗'}")
                print(f"   🔄 Actual Flow: {actual_flow}")
                print(f"   📝 Response Type: {response_type}")
                print(f"   📏 Message Length: {message_length} characters")
                print(f"   ⏱️ Processing Time: {processing_time:.2f} seconds")
                print(f"   🎯 Test Result: {'✅ PASS' if test_result['success'] else '❌ FAIL'}")
                
                # Show response preview
                message_preview = response.get('message', '')[:200] + "..." if len(response.get('message', '')) > 200 else response.get('message', '')
                print(f"   💬 Response Preview: {message_preview}")
                
            except Exception as e:
                print(f"❌ Error processing test {test_case['id']}: {e}")
                test_result = {
                    'test_id': test_case['id'],
                    'question': test_case['question'],
                    'error': str(e),
                    'success': False,
                    'category': test_case['category']
                }
                results.append(test_result)
            
            print("\n" + "=" * 60 + "\n")
        
        # Generate summary report
        print("📊 TEST SUMMARY REPORT")
        print("=" * 60)
        
        total_tests = len(results)
        successful_tests = sum(1 for r in results if r.get('success', False))
        success_rate = (successful_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"📈 Overall Results: {successful_tests}/{total_tests} tests passed ({success_rate:.1f}%)")
        print()
        
        # Category breakdown
        categories = {}
        for result in results:
            category = result.get('category', 'Unknown')
            if category not in categories:
                categories[category] = {'total': 0, 'passed': 0}
            categories[category]['total'] += 1
            if result.get('success', False):
                categories[category]['passed'] += 1
        
        print("📋 Results by Category:")
        for category, stats in categories.items():
            rate = (stats['passed'] / stats['total']) * 100 if stats['total'] > 0 else 0
            print(f"   {category}: {stats['passed']}/{stats['total']} ({rate:.1f}%)")
        print()
        
        # Intent detection accuracy
        intent_matches = sum(1 for r in results if r.get('intent_match', False))
        intent_accuracy = (intent_matches / total_tests) * 100 if total_tests > 0 else 0
        print(f"🎯 Intent Detection Accuracy: {intent_matches}/{total_tests} ({intent_accuracy:.1f}%)")
        
        # Performance metrics
        processing_times = [r.get('processing_time', 0) for r in results if 'processing_time' in r]
        if processing_times:
            avg_time = sum(processing_times) / len(processing_times)
            max_time = max(processing_times)
            min_time = min(processing_times)
            print(f"⏱️ Performance Metrics:")
            print(f"   Average Processing Time: {avg_time:.2f} seconds")
            print(f"   Fastest Response: {min_time:.2f} seconds")
            print(f"   Slowest Response: {max_time:.2f} seconds")
        
        print()
        
        # Detailed results for failed tests
        failed_tests = [r for r in results if not r.get('success', False)]
        if failed_tests:
            print("❌ Failed Tests Details:")
            for test in failed_tests:
                print(f"   Test {test['test_id']}: {test.get('question', 'Unknown')[:50]}...")
                if 'error' in test:
                    print(f"      Error: {test['error']}")
                else:
                    print(f"      Expected: {test.get('expected_intent', 'Unknown')}")
                    print(f"      Detected: {test.get('detected_intent', 'Unknown')}")
        
        print("\n" + "=" * 60)
        
        if success_rate >= 80:
            print("🎉 EXCELLENT! Vector chatbot flow is working well!")
        elif success_rate >= 60:
            print("✅ GOOD! Vector chatbot flow is mostly working, minor issues to address.")
        else:
            print("⚠️ NEEDS IMPROVEMENT! Vector chatbot flow has significant issues.")
        
        return success_rate >= 80
        
    except Exception as e:
        print(f"❌ Critical error in test setup: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run the vector chatbot flow test"""
    print(f"🕒 Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    success = test_vector_chatbot_flow()
    
    print()
    print(f"🕒 Test completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
