#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Vector Chatbot Test - 10 Strategic Questions
Tests the 6-step vector process with focused questions
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def quick_test():
    """Quick test with 10 strategic questions"""
    
    print("🚀 Quick Vector Chatbot Test - 10 Strategic Questions")
    print("=" * 60)
    
    # 10 strategic test questions covering all major flows
    questions = [
        # 1. Greeting (Step 6 - Regular flow)
        "Hello! Good morning, how are you?",
        
        # 2. Blockchain job search (Step 5 - Job feedback)
        "Tell me about blockchain developer jobs and salary",
        
        # 3. Salary inquiry (Step 5 - Salary feedback)  
        "What's the salary for AI/ML Engineer in Vietnam?",
        
        # 4. CV review (Step 5 - CV feedback)
        "Can you review my CV and give feedback?",
        
        # 5. Python jobs (Step 5 - Job feedback)
        "What Python developer opportunities do you have?",
        
        # 6. Company info (Step 6 - Regular flow)
        "Tell me about FOIS ICT PRO company",
        
        # 7. Platform help (Step 6 - Regular flow)
        "How do I use this chatbot platform?",
        
        # 8. DevOps requirements (Step 5 - Job feedback)
        "What are DevOps Engineer job requirements?",
        
        # 9. Remote work (Step 5 - Job feedback)
        "I want remote cloud computing jobs",
        
        # 10. Out of scope (Step 6 - Regular flow)
        "What's the weather today?"
    ]
    
    try:
        from vector_chatbot.vector_chatbot_router import VectorChatbotRouter
        
        print("🔧 Initializing router...")
        router = VectorChatbotRouter()
        print("✅ Router ready!\n")
        
        results = []
        
        for i, question in enumerate(questions, 1):
            print(f"🧪 Test {i}: {question}")
            
            try:
                response = router.process_user_input(
                    user_input=question,
                    user_id=f"test_{i}",
                    conversation_history=[]
                )
                
                intent = response.get('vector_intent', 'UNKNOWN')
                response_type = response.get('response_type', 'unknown')
                message_len = len(response.get('message', ''))
                
                print(f"   🎯 Intent: {intent}")
                print(f"   📝 Type: {response_type}")
                print(f"   📏 Length: {message_len} chars")
                
                # Check if blockchain data is present for blockchain questions
                if 'blockchain' in question.lower():
                    has_blockchain = 'blockchain' in response.get('message', '').lower()
                    print(f"   🔗 Blockchain data: {'✅' if has_blockchain else '❌'}")
                
                results.append({
                    'question': question,
                    'intent': intent,
                    'success': True,
                    'message_length': message_len
                })
                
                print("   ✅ SUCCESS\n")
                
            except Exception as e:
                print(f"   ❌ ERROR: {e}\n")
                results.append({
                    'question': question,
                    'error': str(e),
                    'success': False
                })
        
        # Summary
        successful = sum(1 for r in results if r.get('success', False))
        total = len(results)
        success_rate = (successful / total) * 100
        
        print("=" * 60)
        print(f"📊 SUMMARY: {successful}/{total} tests passed ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            print("🎉 EXCELLENT! Vector chatbot is working well!")
        elif success_rate >= 60:
            print("✅ GOOD! Minor issues to address.")
        else:
            print("⚠️ NEEDS WORK! Significant issues found.")
        
        return success_rate >= 80
        
    except Exception as e:
        print(f"❌ Setup error: {e}")
        return False


if __name__ == "__main__":
    success = quick_test()
    sys.exit(0 if success else 1)
