#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Intent Detection Module cho Email Chatbot
Xử lý 11 loại intent khác nhau
"""

import re
import json
from enum import Enum
from typing import Dict, List, Optional
from dataclasses import dataclass


class IntentType(Enum):
    """Enum cho các loại intent - updated to match vector chatbot intents"""
    GREETING = "greeting"
    FAREWELL = "farewell"
    ASK_COMPANY_INFO = "ask_company_info"
    ASK_BOT_INFO = "ask_bot_info"
    SEARCH_JOBS = "search_jobs"
    FILTER_JOBS = "filter_jobs"
    APPLY_JOB = "apply_job"
    CANCEL_APPLICATION = "cancel_application"
    UPLOAD_RESUME = "upload_resume"
    UPDATE_RESUME = "update_resume"
    GET_APPLICATION_STATUS = "get_application_status"
    JOB_RECOMMENDATION = "job_recommendation"
    FOLLOW_UP_JOB = "follow_up_job"
    LOCATION_QUERY = "location_query"
    TECH_STACK_QUERY = "tech_stack_query"
    SALARY_QUERY = "salary_query"
    INTERVIEW_PROCESS = "interview_process"
    CV_FEEDBACK = "cv_feedback"
    SMALLTALK = "smalltalk"
    OFF_TOPIC = "off_topic"
    THANK_YOU = "thank_you"
    COMPLAINT = "complaint"
    BUG_REPORT = "bug_report"
    REQUEST_HUMAN_SUPPORT = "request_human_support"
    JOB_IT_TRENDING = "job_it_trending"
    OTHER = "other"


@dataclass
class IntentResult:
    """Kết quả phân tích intent"""
    intent: IntentType
    confidence: float
    entities: Dict[str, str]
    context_needed: bool = False


class IntentDetector:
    """Class phát hiện intent từ text sử dụng AI"""

    def __init__(self, gemini_ai=None):
        """Khởi tạo Intent Detector với AI"""
        self.gemini_ai = gemini_ai
        self.intent_descriptions = self._load_intent_descriptions()
        self.follow_up_indicators = self._load_follow_up_indicators()

        # Fallback patterns cho trường hợp AI không khả dụng
        self.fallback_patterns = self._load_fallback_patterns()

    def _load_intent_descriptions(self) -> Dict[IntentType, str]:
        """Load mô tả chi tiết cho từng intent để AI hiểu"""
        return {
            IntentType.GREETING: """
Chào hỏi đơn thuần, lời chào xã giao.
Ví dụ: "Xin chào", "Hello", "Chào bạn", "Hi", "Good morning"
""",

            IntentType.FAREWELL: """
Lời chào tạm biệt, kết thúc cuộc trò chuyện.
Ví dụ: "Tạm biệt", "Goodbye", "Bye", "See you later", "Chào tạm biệt"
""",

            IntentType.ASK_COMPANY_INFO: """
Hỏi về thông tin công ty FOIS ICT PRO, muốn biết công ty làm gì, giới thiệu công ty.
Ví dụ: "FOIS là gì?", "Công ty làm gì?", "Giới thiệu về công ty", "FOIS ICT PRO hoạt động như thế nào?"
""",

            IntentType.ASK_BOT_INFO: """
Hỏi về thông tin bot, cách sử dụng hệ thống, hướng dẫn sử dụng platform.
Ví dụ: "Bot này làm gì?", "Cách sử dụng hệ thống", "Hướng dẫn dùng app", "How to use this platform?"
""",

            IntentType.SEARCH_JOBS: """
Tìm kiếm cơ hội việc làm, muốn xem danh sách job, tìm việc theo kỹ năng/vị trí.
Ví dụ: "Tìm job Python", "Có việc làm developer không?", "Muốn xem job opportunities", "Tuyển dụng gì?"
""",

            IntentType.FILTER_JOBS: """
Lọc job theo tiêu chí cụ thể, hỏi chi tiết về một job cụ thể, yêu cầu công việc.
Ví dụ: "Chi tiết job này", "Job requirements là gì?", "Mô tả công việc", "Yêu cầu vị trí này"
""",

            IntentType.CV_FEEDBACK: """
Muốn gửi CV, chia sẻ hồ sơ cá nhân, ứng tuyển vào vị trí, xin feedback về CV.
Ví dụ: "Gửi CV", "Tôi muốn ứng tuyển", "Đây là hồ sơ của tôi", "Review CV giúp tôi"
""",

            IntentType.SALARY_QUERY: """
Hỏi về mức lương, thu nhập, compensation package.
Ví dụ: "Lương bao nhiêu?", "Mức thu nhập", "Salary range", "Compensation"
""",

            IntentType.JOB_IT_TRENDING: """
Hỏi về xu hướng công nghệ IT, trending technologies, hot skills trong ngành.
Ví dụ: "Công nghệ nào đang hot?", "Xu hướng IT 2024", "Skills nào đang trending?", "Technology trends"
""",

            IntentType.SMALLTALK: """
Trò chuyện phiếm, câu chuyện nhỏ không liên quan đến công việc.
Ví dụ: "Hôm nay thế nào?", "Thời tiết đẹp nhé", "Cuối tuần vui vẻ"
""",

            IntentType.OFF_TOPIC: """
Các câu hỏi ngoài phạm vi tuyển dụng/việc làm, không liên quan đến công ty hay platform.
Ví dụ: "Thời tiết hôm nay", "Tôi thích ăn phở", "Bóng đá", "Tin tức"
""",

            IntentType.THANK_YOU: """
Cảm ơn, biết ơn, appreciation.
Ví dụ: "Cảm ơn", "Thank you", "Thanks", "Cảm ơn bạn nhiều"
""",

            IntentType.OTHER: """
Các intent khác không thuộc các category trên.
Ví dụ: Các câu hỏi không rõ ràng, không phân loại được
"""
        }

    def _load_fallback_patterns(self) -> Dict[IntentType, List[str]]:
        """Load các pattern cho từng intent"""
        return {
            IntentType.GREETING: [
                r'\b(xin chào|chào|hello|hi|hey)\b',
                r'\b(chào bạn|chào anh|chào chị)\b',
                r'\b(good morning|good afternoon|good evening)\b'
            ],

            IntentType.FAREWELL: [
                r'\b(tạm biệt|goodbye|bye|see you)\b',
                r'\b(chào tạm biệt|hẹn gặp lại)\b'
            ],

            IntentType.ASK_COMPANY_INFO: [
                r'\b(fois|công ty)\b.*\b(là gì|giới thiệu|thông tin)\b',
                r'\b(về công ty|company info|about company)\b',
                r'\b(công ty làm gì|hoạt động gì)\b'
            ],

            IntentType.ASK_BOT_INFO: [
                r'\b(bot|chatbot)\b.*\b(là gì|làm gì|giúp gì)\b',
                r'\b(cách dùng|hướng dẫn|sử dụng)\b.*\b(web|app|hệ thống|platform)\b',
                r'\b(làm sao để|how to)\b.*\b(dùng|use)\b'
            ],

            IntentType.SEARCH_JOBS: [
                r'\b(tìm việc|job|công việc|vị trí)\b',
                r'\b(có việc|có job|có vị trí)\b.*\b(nào|gì|không)\b',
                r'\b(muốn xem|xem job|job opportunities)\b',
                r'\b(tuyển dụng|recruitment|hiring)\b'
            ],

            IntentType.FILTER_JOBS: [
                r'\b(chi tiết|details|thông tin)\b.*\b(job|việc|vị trí)\b',
                r'\b(job.*yêu cầu|requirements|qualification)\b',
                r'\b(mô tả công việc|job description)\b'
            ],

            IntentType.CV_FEEDBACK: [
                r'\b(gửi cv|send cv|cv|resume)\b',
                r'\b(hồ sơ|profile|thông tin cá nhân)\b',
                r'\b(ứng tuyển|apply|application)\b',
                r'\b(review cv|feedback cv|đánh giá cv)\b'
            ],

            IntentType.SALARY_QUERY: [
                r'\b(lương|salary|mức lương|pay)\b',
                r'\b(thu nhập|income|compensation)\b',
                r'\b(bao nhiêu|how much|range)\b.*\b(lương|salary)\b'
            ],

            IntentType.JOB_IT_TRENDING: [
                r'\b(trending|xu hướng|hot)\b.*\b(technology|công nghệ|skill)\b',
                r'\b(công nghệ.*hot|skills.*trending)\b'
            ],

            IntentType.THANK_YOU: [
                r'\b(cảm ơn|thank you|thanks|cám ơn)\b'
            ],

            IntentType.OFF_TOPIC: [
                r'\b(thời tiết|weather|bóng đá|football)\b',
                r'\b(ăn uống|food|phim|movie)\b'
            ]
        }

    def _load_follow_up_indicators(self) -> List[str]:
        """Load các indicator cho FOLLOW_UP intent"""
        return [
            r'\b(vậy|thế|thì|còn|nữa|tiếp theo)\b',
            r'\b(cái.*kia|job.*đó|việc.*đó)\b',
            r'\b(có.*không|có.*gì|sao|nhỉ)\b',
            r'\b(à đúng rồi|ờ|ừm|hmm)\b'
        ]

    def detect_intent(self, text: str, has_context: bool = False) -> IntentResult:
        """
        Phát hiện intent từ text sử dụng AI

        Args:
            text: Nội dung cần phân tích
            has_context: Có context cuộc hội thoại trước không

        Returns:
            IntentResult: Kết quả phân tích intent
        """
        text_lower = text.lower().strip()

        # Kiểm tra FOLLOW_UP trước nếu có context (vẫn dùng rule-based)
        if has_context and self._is_follow_up_simple(text_lower):
            return IntentResult(
                intent=IntentType.OTHER,
                confidence=0.9,
                entities=self._extract_follow_up_entities(text_lower),
                context_needed=True
            )

        # Sử dụng AI để detect intent
        if self.gemini_ai and self.gemini_ai.is_available():
            try:
                ai_result = self._detect_intent_with_ai(text)
                if ai_result:
                    return ai_result
            except Exception as e:
                print(f"⚠️ AI intent detection failed: {str(e)}")

        # Fallback về pattern matching
        print("📝 Fallback to pattern matching")
        return self._detect_intent_fallback(text_lower)

    def _detect_intent_with_ai(self, text: str) -> IntentResult:
        """Sử dụng AI để detect intent với structured output"""

        # Tạo prompt cho AI
        intent_list = []
        for intent_type, description in self.intent_descriptions.items():
            intent_list.append(
                f"**{intent_type.value}**: {description.strip()}")

        prompt = f"""
Bạn là một AI chuyên phân loại ý định (intent) của người dùng trong hệ thống tuyển dụng.

DANH SÁCH CÁC INTENT:
{chr(10).join(intent_list)}

NHIỆM VỤ:
Phân tích câu sau và xác định intent phù hợp nhất: "{text}"

YÊU CẦU PHẢN HỒI:
Trả lời CHÍNH XÁC theo format JSON sau:
{{
    "intent": "TÊN_INTENT",
    "confidence": 0.95,
    "entities": {{
        "skills": "python, react",
        "position": "developer",
        "salary_range": "20-30 triệu",
        "location": "Hà Nội"
    }},
    "reasoning": "Lý do ngắn gọn"
}}

LƯU Ý:
- intent phải là một trong: {', '.join([i.value for i in IntentType])}
- confidence từ 0.0 đến 1.0
- entities chỉ trích xuất nếu có (skills, position, salary_range, location, question_type, reference_type)
- reasoning giải thích ngắn gọn tại sao chọn intent này
"""

        try:
            # Sử dụng structured output
            result = self.gemini_ai.generate_structured_content(prompt)

            # Validate intent
            intent_value = result.get('intent', 'OTHER')
            try:
                intent_type = IntentType(intent_value)
            except ValueError:
                intent_type = IntentType.OTHER

            confidence = float(result.get('confidence', 0.5))
            entities = result.get('entities', {})
            reasoning = result.get('reasoning', '')

            print(
                f"🤖 AI detected: {intent_type.value} (confidence: {confidence:.2f})")
            if reasoning:
                print(f"💭 Reasoning: {reasoning}")

            return IntentResult(
                intent=intent_type,
                confidence=confidence,
                entities=entities,
                context_needed=(intent_type == IntentType.OTHER)
            )

        except Exception as e:
            print(f"❌ Error with AI structured detection: {str(e)}")
            return None

    def _detect_intent_fallback(self, text: str) -> IntentResult:
        """Fallback detection sử dụng pattern matching"""
        best_intent = IntentType.OTHER
        best_confidence = 0.0
        entities = {}

        for intent_type, patterns in self.fallback_patterns.items():
            confidence = self._calculate_confidence(text, patterns)
            if confidence > best_confidence:
                best_confidence = confidence
                best_intent = intent_type
                entities = self._extract_entities(text, intent_type)

        # Nếu confidence thấp, coi là OTHER
        if best_confidence < 0.3:
            best_intent = IntentType.OTHER
            best_confidence = 0.5

        return IntentResult(
            intent=best_intent,
            confidence=best_confidence,
            entities=entities,
            context_needed=(best_intent == IntentType.OTHER)
        )

    def _is_follow_up_simple(self, text: str) -> bool:
        """Kiểm tra có phải FOLLOW_UP intent không"""
        # Kiểm tra độ dài câu (FOLLOW_UP thường ngắn)
        if len(text.split()) > 15:
            return False

        # Kiểm tra có follow-up indicators
        follow_up_score = 0
        for pattern in self.follow_up_indicators:
            if re.search(pattern, text):
                follow_up_score += 1

        # Kiểm tra thiếu chủ ngữ rõ ràng
        lacks_subject = not any(word in text for word in [
            'tôi', 'mình', 'em', 'anh', 'chị', 'i want', 'i need'
        ])

        if lacks_subject:
            follow_up_score += 1

        return follow_up_score >= 2

    def _calculate_confidence(self, text: str, patterns: List[str]) -> float:
        """Tính confidence score cho intent"""
        matches = 0
        total_patterns = len(patterns)

        for pattern in patterns:
            if re.search(pattern, text):
                matches += 1

        return matches / total_patterns if total_patterns > 0 else 0.0

    def _extract_entities(self, text: str, intent_type: IntentType) -> Dict[str, str]:
        """Trích xuất entities từ text theo intent"""
        entities = {}

        if intent_type == IntentType.SEARCH_JOBS:
            # Trích xuất skill, position
            skills = re.findall(
                r'\b(python|java|javascript|react|nodejs|backend|frontend|fullstack|devops)\b', text)
            if skills:
                entities['skills'] = ', '.join(skills)

            positions = re.findall(
                r'\b(developer|engineer|programmer|designer|tester|manager)\b', text)
            if positions:
                entities['position'] = ', '.join(positions)

        elif intent_type == IntentType.SALARY_QUERY:
            # Trích xuất số tiền
            salary_numbers = re.findall(
                r'\b(\d+(?:\.\d+)?)\s*(triệu|million|k|nghìn)\b', text)
            if salary_numbers:
                entities['salary_range'] = str(salary_numbers[0])

        return entities

    def _extract_follow_up_entities(self, text: str) -> Dict[str, str]:
        """Trích xuất entities cho FOLLOW_UP intent"""
        entities = {}

        # Trích xuất từ khóa tham chiếu
        if 'job' in text or 'việc' in text:
            entities['reference_type'] = 'job'

        if 'remote' in text:
            entities['question_type'] = 'remote'
        elif 'yêu cầu' in text or 'requirement' in text:
            entities['question_type'] = 'requirements'
        elif 'lương' in text or 'salary' in text:
            entities['question_type'] = 'salary'

        return entities


# Factory function
def create_intent_detector(gemini_ai=None) -> IntentDetector:
    """Tạo Intent Detector instance với AI"""
    return IntentDetector(gemini_ai)
