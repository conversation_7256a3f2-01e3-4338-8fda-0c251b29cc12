# 🧪 Test Scenarios for FOIS ICT PRO Chatbot

## 📋 Overview
This document provides comprehensive test scenarios for all supported user intents in the FOIS ICT PRO chatbot system. Each scenario includes sample user inputs and expected bot behaviors.

---

## 🎯 Intent Categories

### 1. **Basic Interactions**
- `greeting` - Initial greetings and conversation starters
- `farewell` - Goodbye messages and conversation endings
- `thank_you` - Expressions of gratitude
- `smalltalk` - Casual conversation

### 2. **Information Queries**
- `ask_company_info` - Questions about FOIS ICT PRO
- `ask_bot_info` - Questions about the chatbot itself
- `location_query` - Office locations and addresses
- `tech_stack_query` - Technology stack and tools used

### 3. **Job-Related Intents**
- `search_jobs` - General job search queries
- `filter_jobs` - Specific job filtering requests
- `job_recommendation` - Personalized job suggestions
- `follow_up_job` - Follow-up questions about specific positions
- `salary_query` - Salary and compensation questions
- `interview_process` - Interview procedures and preparation
- `job_it_trending` - IT market trends and hot technologies

### 4. **Application Process**
- `apply_job` - Job application submissions
- `cancel_application` - Application cancellations
- `get_application_status` - Application status inquiries
- `upload_resume` - CV/Resume upload requests
- `update_resume` - CV/Resume update requests
- `cv_feedback` - CV review and improvement suggestions

### 5. **Support & Issues**
- `complaint` - User complaints and concerns
- `bug_report` - Technical issues and bug reports
- `request_human_support` - Escalation to human agents
- `off_topic` - Out-of-scope questions

---

## 🧪 Test Scenarios

### **Greeting Intent**
```
🎯 Intent: greeting
📝 Sample Inputs:
- "Hello"
- "Hi there"
- "Good morning"
- "Chào bạn"
- "Xin chào"

✅ Expected Response:
- Friendly greeting in user's language
- Brief introduction to FOIS ICT PRO
- Offer to help with job search or company information
- Include suggestion buttons for next actions
```

### **Company Information Intent**
```
🎯 Intent: ask_company_info
📝 Sample Inputs:
- "Tell me about FOIS ICT PRO"
- "What does your company do?"
- "Company information"
- "Thông tin về công ty"
- "FOIS ICT PRO là gì?"

✅ Expected Response:
- Company overview and mission
- Services and expertise areas
- Company culture and values
- Contact information
- Suggestion for job opportunities
```

### **Job Search Intent**
```
🎯 Intent: search_jobs
📝 Sample Inputs:
- "What jobs do you have?"
- "Show me available positions"
- "I'm looking for a job"
- "Có vị trí nào đang tuyển không?"
- "Tôi muốn tìm việc"

✅ Expected Response:
- List of available positions (ONLY real positions from data):
  • Senior Python Developer: 25-35 triệu VNĐ - Hà Nội
  • React Frontend Developer: 20-30 triệu VNĐ - TP.HCM
  • Senior DevOps Engineer: 40-60 triệu VNĐ - Remote
- Brief requirements for each position
- Next steps for application
- NO fake positions or salaries
```

### **Salary Query Intent**
```
🎯 Intent: salary_query
📝 Sample Inputs:
- "What's the salary range?"
- "How much do you pay?"
- "Salary information"
- "Mức lương như thế nào?"
- "Lương bao nhiêu?"

✅ Expected Response:
- Salary ranges by experience level:
  • Junior (0-2 years): 15-25 triệu VNĐ
  • Mid-level (2-5 years): 25-40 triệu VNĐ
  • Senior (5+ years): 40-60 triệu VNĐ
- Specific position salaries (only real data)
- Benefits and compensation package info
- Performance-based increases mention
```

### **CV Feedback Intent**
```
🎯 Intent: cv_feedback
📝 Sample Inputs:
- "Can you help me improve my CV?"
- "Review my resume"
- "CV feedback"
- "Bạn có thể xem CV của tôi không?"
- "Góp ý CV"

✅ Expected Response:
- Offer to review uploaded CV
- General CV improvement tips
- Industry-specific recommendations
- Format and structure advice
- Request to upload CV for detailed feedback
```

### **Job IT Trending Intent**
```
🎯 Intent: job_it_trending
📝 Sample Inputs:
- "What are the hot IT jobs?"
- "IT market trends"
- "Which technologies are in demand?"
- "Công nghệ nào đang hot?"
- "Xu hướng IT 2024"

✅ Expected Response:
- Current IT market trends
- Hot technologies and skills
- Salary ranges for trending positions
- Market demand analysis
- FOIS ICT PRO's relevant positions
- Future outlook and recommendations
```

### **Application Process Intent**
```
🎯 Intent: apply_job
📝 Sample Inputs:
- "I want to apply for Python Developer"
- "How do I apply?"
- "Submit application"
- "Tôi muốn ứng tuyển"
- "Làm sao để nộp đơn?"

✅ Expected Response:
- Application process steps
- Required documents (CV, cover letter)
- Application timeline
- Contact information
- Next steps after submission
- Interview process overview
```

### **Technical Support Intent**
```
🎯 Intent: bug_report
📝 Sample Inputs:
- "The website is not working"
- "I found a bug"
- "Technical issue"
- "Trang web bị lỗi"
- "Có vấn đề kỹ thuật"

✅ Expected Response:
- Acknowledge the issue
- Request specific details about the problem
- Provide troubleshooting steps
- Offer alternative contact methods
- Escalation to technical team if needed
```

### **Off-Topic Intent**
```
🎯 Intent: off_topic
📝 Sample Inputs:
- "What's the weather like?"
- "Tell me a joke"
- "How to cook pasta?"
- "Thời tiết hôm nay thế nào?"
- "Kể chuyện cười đi"

✅ Expected Response:
- Polite acknowledgment
- Redirect to job-related topics
- Offer assistance with career/job questions
- Maintain friendly but professional tone
- Suggest relevant services
```

### **Farewell Intent**
```
🎯 Intent: farewell
📝 Sample Inputs:
- "Goodbye"
- "See you later"
- "Bye"
- "Tạm biệt"
- "Chào tạm biệt"

✅ Expected Response:
- Polite farewell message
- Thank user for their time
- Invite them to return anytime
- Offer final assistance if needed
- Professional closing
```

### **Thank You Intent**
```
🎯 Intent: thank_you
📝 Sample Inputs:
- "Thank you"
- "Thanks a lot"
- "I appreciate it"
- "Cảm ơn bạn"
- "Cảm ơn nhiều"

✅ Expected Response:
- Acknowledge gratitude gracefully
- Express pleasure in helping
- Offer continued assistance
- Maintain positive tone
- Suggest next steps if applicable
```

### **Small Talk Intent**
```
🎯 Intent: smalltalk
📝 Sample Inputs:
- "How are you?"
- "Nice to meet you"
- "How's your day?"
- "Bạn khỏe không?"
- "Hôm nay thế nào?"

✅ Expected Response:
- Friendly acknowledgment
- Brief positive response
- Redirect to professional topics
- Maintain conversational tone
- Offer assistance with job-related matters
```

### **Bot Information Intent**
```
🎯 Intent: ask_bot_info
📝 Sample Inputs:
- "What can you do?"
- "Tell me about yourself"
- "What are your capabilities?"
- "Bạn có thể làm gì?"
- "Giới thiệu về bạn"

✅ Expected Response:
- Explain chatbot capabilities
- List available services (job search, CV help, company info)
- Mention AI-powered assistance
- Highlight key features
- Invite user to try services
```

### **Location Query Intent**
```
🎯 Intent: location_query
📝 Sample Inputs:
- "Where is your office?"
- "Company address"
- "Office locations"
- "Địa chỉ công ty ở đâu?"
- "Văn phòng ở đâu?"

✅ Expected Response:
- Provide office addresses (only real locations)
- Include contact information
- Mention working arrangements (remote/hybrid)
- Directions or map references if available
- NO fake addresses
```

### **Tech Stack Query Intent**
```
🎯 Intent: tech_stack_query
📝 Sample Inputs:
- "What technologies do you use?"
- "Tech stack information"
- "Programming languages used"
- "Công nghệ nào được sử dụng?"
- "Ngôn ngữ lập trình gì?"

✅ Expected Response:
- List technologies used at FOIS ICT PRO
- Programming languages and frameworks
- Development tools and platforms
- Technology trends and adoption
- Relevance to available positions
```

### **Filter Jobs Intent**
```
🎯 Intent: filter_jobs
📝 Sample Inputs:
- "Show me Python jobs only"
- "Remote positions available?"
- "Jobs in Ho Chi Minh City"
- "Vị trí remote có không?"
- "Công việc Python ở đâu?"

✅ Expected Response:
- Filtered job list based on criteria
- Only show matching real positions
- Include relevant details (salary, location)
- Suggest related positions if limited results
- NO fake filtered results
```

### **Job Recommendation Intent**
```
🎯 Intent: job_recommendation
📝 Sample Inputs:
- "What job suits me?"
- "Recommend a position for me"
- "Best job for my skills"
- "Vị trí nào phù hợp với tôi?"
- "Gợi ý công việc"

✅ Expected Response:
- Ask about user's skills and experience
- Recommend from available real positions
- Explain why position suits them
- Mention growth opportunities
- Suggest next steps for application
```

### **Follow-up Job Intent**
```
🎯 Intent: follow_up_job
📝 Sample Inputs:
- "Tell me more about Python Developer"
- "Details about that position"
- "What are the requirements?"
- "Chi tiết về vị trí đó"
- "Yêu cầu công việc gì?"

✅ Expected Response:
- Detailed information about specific position
- Requirements and qualifications
- Responsibilities and duties
- Team structure and work environment
- Application process for that role
```

### **Interview Process Intent**
```
🎯 Intent: interview_process
📝 Sample Inputs:
- "What's the interview process?"
- "How many interview rounds?"
- "Interview preparation tips"
- "Quy trình phỏng vấn như thế nào?"
- "Chuẩn bị phỏng vấn gì?"

✅ Expected Response:
- Interview stages and timeline
- Types of interviews (technical, HR, etc.)
- Preparation recommendations
- What to expect in each round
- Tips for success
```

### **Cancel Application Intent**
```
🎯 Intent: cancel_application
📝 Sample Inputs:
- "I want to cancel my application"
- "Withdraw my application"
- "Cancel job application"
- "Tôi muốn hủy đơn ứng tuyển"
- "Rút đơn xin việc"

✅ Expected Response:
- Acknowledge cancellation request
- Confirm application details
- Process cancellation steps
- Ask for reason (optional)
- Keep door open for future applications
```

### **Application Status Intent**
```
🎯 Intent: get_application_status
📝 Sample Inputs:
- "What's my application status?"
- "Check application progress"
- "Any updates on my application?"
- "Tình trạng đơn ứng tuyển thế nào?"
- "Có tin gì về đơn xin việc không?"

✅ Expected Response:
- Request application reference/details
- Explain status checking process
- Provide general timeline information
- Offer alternative contact methods
- Set realistic expectations
```

### **Upload Resume Intent**
```
🎯 Intent: upload_resume
📝 Sample Inputs:
- "I want to upload my CV"
- "Submit my resume"
- "Upload CV file"
- "Tôi muốn gửi CV"
- "Upload hồ sơ"

✅ Expected Response:
- Provide upload instructions
- Mention supported file formats
- Explain CV review process
- Offer CV improvement tips
- Guide through upload steps
```

### **Update Resume Intent**
```
🎯 Intent: update_resume
📝 Sample Inputs:
- "Update my CV"
- "Change my resume"
- "Modify my profile"
- "Cập nhật CV"
- "Sửa hồ sơ"

✅ Expected Response:
- Guide through update process
- Explain what can be modified
- Offer improvement suggestions
- Mention review timeline
- Provide support if needed
```

### **Complaint Intent**
```
🎯 Intent: complaint
📝 Sample Inputs:
- "I have a complaint"
- "This service is not good"
- "I'm not satisfied"
- "Tôi có khiếu nại"
- "Dịch vụ không tốt"

✅ Expected Response:
- Acknowledge concern professionally
- Apologize for any inconvenience
- Request specific details
- Offer resolution steps
- Escalate to appropriate team
```

### **Request Human Support Intent**
```
🎯 Intent: request_human_support
📝 Sample Inputs:
- "I need to talk to a human"
- "Connect me to support staff"
- "Speak with a person"
- "Tôi muốn nói chuyện với người thật"
- "Kết nối với nhân viên"

✅ Expected Response:
- Acknowledge request for human support
- Provide contact information
- Explain availability hours
- Offer immediate assistance if possible
- Set expectations for response time
```

---

## 🔧 Testing Guidelines

### **Response Quality Checks**
- ✅ Response uses ONLY real data from sample_data.py
- ✅ No fake URLs, positions, or salary ranges
- ✅ Complete responses (no truncation at "các vị trí sau:")
- ✅ Appropriate language (Vietnamese/English based on input)
- ✅ Professional but friendly tone
- ✅ Includes actionable next steps

### **Context Preservation Tests**
- ✅ Multi-turn conversations (5+ exchanges)
- ✅ Context maintained across different intents
- ✅ No repetitive responses to new questions
- ✅ Proper conversation flow

### **Edge Cases**
- ✅ Mixed language inputs
- ✅ Typos and misspellings
- ✅ Very short/long messages
- ✅ Rapid successive messages
- ✅ Ambiguous queries

---

## 📊 Success Metrics

### **Response Accuracy**
- Intent classification accuracy: >95%
- Response relevance: >90%
- Data accuracy: 100% (no fake data)

### **User Experience**
- Response time: <3 seconds
- Conversation completion rate: >80%
- User satisfaction: >4/5 stars

### **Technical Performance**
- Token usage optimization: <90% of limit
- Context preservation: 5+ message history
- Error rate: <5%

---

## 🚀 Quick Test Commands

```bash
# Start the chatbot
python3 web_app.py --auto-port

# Run specific intent tests
python3 test_intent_scenarios.py --intent greeting
python3 test_intent_scenarios.py --intent search_jobs
python3 test_intent_scenarios.py --intent cv_feedback

# Run full test suite
python3 test_all_intents.py
```

---

## 🎯 Multi-Turn Conversation Scenarios

### **Scenario 1: Job Search Journey**
```
1. User: "Hello" (greeting)
2. Bot: Welcome + job search offer
3. User: "What jobs do you have?" (search_jobs)
4. Bot: List real positions
5. User: "Tell me about Python Developer" (follow_up_job)
6. Bot: Specific position details
7. User: "What's the salary?" (salary_query)
8. Bot: Salary range for that position
9. User: "How do I apply?" (apply_job)
10. Bot: Application process

✅ Expected: Each response should be contextual and relevant
❌ Avoid: Repetitive responses or context loss
```

### **Scenario 2: CV Improvement Journey**
```
1. User: "Can you help improve my CV?" (cv_feedback)
2. Bot: CV improvement offer
3. User: "What should I include?" (cv_feedback)
4. Bot: CV structure recommendations
5. User: "Any tips for Python developer CV?" (cv_feedback + tech_stack_query)
6. Bot: Python-specific CV advice
7. User: "What about salary expectations?" (salary_query)
8. Bot: Salary guidance for CV

✅ Expected: Coherent advice flow
❌ Avoid: Generic responses ignoring previous context
```

### **Scenario 3: Company Research Journey**
```
1. User: "Tell me about FOIS ICT PRO" (ask_company_info)
2. Bot: Company overview
3. User: "What technologies do you use?" (tech_stack_query)
4. Bot: Technology stack info
5. User: "Where are your offices?" (location_query)
6. Bot: Office locations
7. User: "What's the work culture like?" (ask_company_info)
8. Bot: Culture and values

✅ Expected: Comprehensive company information
❌ Avoid: Contradictory information across responses
```

---

## 🔍 Debugging Scenarios

### **Context Loss Test**
```
Purpose: Verify conversation history optimization
Steps:
1. Ask 10 different questions in sequence
2. Check if bot maintains context
3. Verify no repetitive responses
4. Monitor token usage in console

Expected Console Logs:
- "🔍 OPTIMIZING CONVERSATION HISTORY"
- "📊 Input: X messages, limit: 5000 tokens"
- "✅ Kept minimum 20 messages for context"
```

### **Data Accuracy Test**
```
Purpose: Ensure no fake data generation
Steps:
1. Ask about job positions
2. Verify only real positions mentioned
3. Check salary ranges match sample_data.py
4. Confirm no fake URLs generated

Red Flags:
- Positions not in allowed list
- Fake website URLs
- Made-up salary ranges
- Non-existent company details
```

---

## 📝 Notes

- All test scenarios should use **real data only** from sample_data.py
- Responses must be **complete and comprehensive**
- **No fake information** should be generated
- Context should be **preserved across 20+ exchanges** (updated from 5+)
- Both **Vietnamese and English** inputs should be supported
- Monitor console logs for optimization and validation warnings
