#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vector Chat Demo Script - 10 Questions for Functionality Demonstration
Perfect for showcasing the vector chatbot capabilities
"""

import sys
import os
import time

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_vector_chat():
    """Demo the vector chat with 10 strategic questions"""
    
    print("🎯 VECTOR CHAT FUNCTIONALITY DEMO")
    print("=" * 50)
    print("Demonstrating the 6-step vector process with 10 key questions")
    print("=" * 50)
    print()
    
    # 10 demo questions showcasing different capabilities
    demo_questions = [
        {
            "id": 1,
            "question": "Hello! I'm looking for a new job opportunity",
            "purpose": "🤝 Greeting & Job Interest",
            "showcases": "Intent detection, friendly response, job guidance"
        },
        {
            "id": 2,
            "question": "Tell me about blockchain developer jobs and salary ranges",
            "purpose": "🔗 Blockchain Technology Focus",
            "showcases": "Comprehensive blockchain data (40-80 triệu VNĐ), market trends"
        },
        {
            "id": 3,
            "question": "What's the salary for AI/ML Engineer positions?",
            "purpose": "🤖 AI/ML Salary Analysis",
            "showcases": "Detailed salary data, market insights, career growth"
        },
        {
            "id": 4,
            "question": "I want to improve my CV for Python developer roles",
            "purpose": "📄 CV Optimization",
            "showcases": "CV guidance, skills recommendations, market-driven advice"
        },
        {
            "id": 5,
            "question": "What Python developer jobs do you have available?",
            "purpose": "🐍 Python Job Search",
            "showcases": "Technology-specific search, job matching, requirements"
        },
        {
            "id": 6,
            "question": "Tell me about FOIS ICT PRO company and your services",
            "purpose": "🏢 Company Information",
            "showcases": "Company background, services, professional presentation"
        },
        {
            "id": 7,
            "question": "What are the requirements for Senior DevOps Engineer?",
            "purpose": "⚙️ DevOps Role Details",
            "showcases": "Specific job requirements, skills, experience levels"
        },
        {
            "id": 8,
            "question": "I'm interested in remote work opportunities in cloud computing",
            "purpose": "🌐 Remote & Cloud Focus",
            "showcases": "Remote filtering, cloud technologies, flexible work"
        },
        {
            "id": 9,
            "question": "What's the job market trend for IT in Vietnam 2024-2025?",
            "purpose": "📈 Market Analysis",
            "showcases": "Market trends, forecasts, industry insights"
        },
        {
            "id": 10,
            "question": "How do I apply for jobs through your platform?",
            "purpose": "🛠️ Platform Usage",
            "showcases": "User guidance, application process, next steps"
        }
    ]
    
    try:
        from vector_chatbot.vector_chatbot_router import VectorChatbotRouter
        
        print("🚀 Initializing Vector Chat System...")
        router = VectorChatbotRouter()
        print("✅ Vector Chat Ready for Demo!\n")
        
        for demo in demo_questions:
            print(f"🎬 DEMO {demo['id']}: {demo['purpose']}")
            print(f"📝 Question: \"{demo['question']}\"")
            print(f"🎯 Showcases: {demo['showcases']}")
            print("-" * 50)
            
            # Process the question
            start_time = time.time()
            
            try:
                response = router.process_user_input(
                    user_input=demo['question'],
                    user_id=f"demo_user_{demo['id']}",
                    conversation_history=[]
                )
                
                end_time = time.time()
                processing_time = end_time - start_time
                
                # Extract key information
                intent = response.get('vector_intent', 'UNKNOWN')
                response_type = response.get('response_type', 'unknown')
                message = response.get('message', '')
                suggestions = response.get('suggestion_answers', [])
                
                print(f"🎯 Detected Intent: {intent}")
                print(f"📊 Response Type: {response_type}")
                print(f"⏱️ Processing Time: {processing_time:.2f} seconds")
                print()
                
                # Show the response
                print("💬 VECTOR CHAT RESPONSE:")
                print("─" * 40)
                print(message)
                print("─" * 40)
                
                # Show suggestions if available
                if suggestions:
                    print("\n💡 Suggested Follow-up Questions:")
                    for i, suggestion in enumerate(suggestions[:3], 1):
                        print(f"   {i}. {suggestion}")
                
                # Highlight key features for specific demos
                if demo['id'] == 2:  # Blockchain demo
                    blockchain_keywords = ['blockchain', '40-80 triệu', 'solidity', 'web3', 'smart contract']
                    found_keywords = [kw for kw in blockchain_keywords if kw.lower() in message.lower()]
                    print(f"\n🔗 Blockchain Data Found: {len(found_keywords)}/5 keywords")
                    for kw in found_keywords:
                        print(f"   ✅ {kw}")
                
                elif demo['id'] == 3:  # AI/ML salary demo
                    salary_keywords = ['ai/ml', 'machine learning', 'triệu', 'salary', 'lương']
                    found_keywords = [kw for kw in salary_keywords if kw.lower() in message.lower()]
                    print(f"\n🤖 AI/ML Salary Data: {len(found_keywords)}/5 keywords")
                    for kw in found_keywords:
                        print(f"   ✅ {kw}")
                
                print("\n" + "=" * 50 + "\n")
                
                # Pause for demo effect (remove for automated testing)
                input("Press Enter to continue to next demo...")
                print()
                
            except Exception as e:
                print(f"❌ Error in demo {demo['id']}: {e}")
                print("=" * 50 + "\n")
        
        # Demo summary
        print("🎉 VECTOR CHAT DEMO COMPLETED!")
        print("=" * 50)
        print("✅ Key Features Demonstrated:")
        print("   🎯 Intent Detection - Accurate classification of user queries")
        print("   📊 Comprehensive Data - Complete blockchain, salary, job information")
        print("   🔄 Smart Routing - Feedback vs regular response handling")
        print("   💬 Natural Responses - AI-generated, contextual answers")
        print("   🎨 Rich Formatting - Emojis, structure, professional presentation")
        print("   💡 Smart Suggestions - Relevant follow-up questions")
        print("   ⚡ Fast Performance - Quick vector processing and response")
        print()
        print("🚀 Vector Chat is ready for production use!")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo setup error: {e}")
        import traceback
        traceback.print_exc()
        return False


def quick_demo():
    """Quick demo without pauses for automated testing"""
    
    print("🎯 QUICK VECTOR CHAT DEMO")
    print("=" * 30)
    
    questions = [
        "Hello! I need a job",
        "Blockchain developer salary?",
        "AI/ML Engineer positions?",
        "Review my CV please",
        "Python jobs available?",
        "About FOIS ICT PRO?",
        "DevOps requirements?",
        "Remote cloud jobs?",
        "IT market trends?",
        "How to apply?"
    ]
    
    try:
        from vector_chatbot.vector_chatbot_router import VectorChatbotRouter
        
        print("🚀 Initializing...")
        router = VectorChatbotRouter()
        print("✅ Ready!\n")
        
        for i, question in enumerate(questions, 1):
            print(f"{i}. {question}")
            
            try:
                response = router.process_user_input(
                    user_input=question,
                    user_id=f"quick_{i}",
                    conversation_history=[]
                )
                
                intent = response.get('vector_intent', 'UNKNOWN')
                message_len = len(response.get('message', ''))
                
                print(f"   🎯 {intent} | 📏 {message_len} chars | ✅ SUCCESS")
                
            except Exception as e:
                print(f"   ❌ ERROR: {e}")
        
        print("\n🎉 Quick demo completed!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def main():
    """Main demo function"""
    print("🎬 Vector Chat Demo Options:")
    print("1. Full Interactive Demo (with pauses)")
    print("2. Quick Demo (no pauses)")
    print()
    
    choice = input("Choose demo type (1 or 2): ").strip()
    
    if choice == "1":
        return demo_vector_chat()
    elif choice == "2":
        return quick_demo()
    else:
        print("Running quick demo by default...")
        return quick_demo()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
