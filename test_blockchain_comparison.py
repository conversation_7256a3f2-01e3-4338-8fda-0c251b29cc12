#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick test to demonstrate blockchain data availability in vector chat
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_blockchain_in_vector_chat():
    """Test blockchain data availability in vector chat"""
    print("🔗 Testing Blockchain Data in Vector Chat")
    print("=" * 50)
    
    try:
        from vector_chatbot.vector_chatbot_router import VectorChatbotRouter
        
        # Initialize router
        print("🚀 Initializing Vector Chatbot Router...")
        router = VectorChatbotRouter()
        
        # Test blockchain-related query
        blockchain_query = "Tell me about blockchain developer jobs and salary"
        
        print(f"\n📝 Query: '{blockchain_query}'")
        print("🔄 Processing with vector chat...")
        
        # Process the query
        response = router.process_user_input(
            user_input=blockchain_query,
            user_id="test_user",
            conversation_history=[]
        )
        
        # Analyze response
        message = response.get('message', '')
        
        print(f"\n📊 Response Analysis:")
        print(f"   - Response length: {len(message)} characters")
        print(f"   - Contains 'blockchain': {'blockchain' in message.lower()}")
        print(f"   - Contains salary info: {'triệu' in message.lower() or 'million' in message.lower()}")
        print(f"   - Response type: {response.get('response_type', 'unknown')}")
        print(f"   - Intent detected: {response.get('vector_intent', 'unknown')}")
        
        # Show first part of response
        print(f"\n💬 Response Preview:")
        preview = message[:500] + "..." if len(message) > 500 else message
        print(preview)
        
        # Check for key blockchain information
        blockchain_keywords = [
            'blockchain', 'solidity', 'web3', 'smart contract', 
            '40-80 triệu', '40-70 triệu', '45-85 triệu'
        ]
        
        found_keywords = []
        for keyword in blockchain_keywords:
            if keyword.lower() in message.lower():
                found_keywords.append(keyword)
        
        print(f"\n🔍 Blockchain Keywords Found: {len(found_keywords)}/{len(blockchain_keywords)}")
        for keyword in found_keywords:
            print(f"   ✅ {keyword}")
        
        if len(found_keywords) >= 3:
            print("\n🎉 SUCCESS: Vector chat has comprehensive blockchain data!")
            return True
        else:
            print("\n❌ ISSUE: Vector chat missing blockchain data")
            return False
            
    except Exception as e:
        print(f"❌ Error testing vector chat: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_comprehensive_market_data():
    """Test comprehensive market data in vector chat"""
    print("\n📊 Testing Comprehensive Market Data")
    print("=" * 50)
    
    try:
        from vector_chatbot.vector_chatbot_router import VectorChatbotRouter
        
        router = VectorChatbotRouter()
        
        # Test comprehensive market context
        context = router._build_comprehensive_market_context()
        
        print(f"📈 Market Context Analysis:")
        print(f"   - Total length: {len(context)} characters")
        
        # Check for key sections
        key_sections = [
            "HOT TECHNOLOGIES & SALARY RANGES",
            "Blockchain: 40-80 triệu VNĐ",
            "TOP PAYING POSITIONS", 
            "Blockchain Engineer: 40-70 triệu VNĐ",
            "2025 FORECAST",
            "Blockchain Dev: 45-85 triệu VNĐ"
        ]
        
        found_sections = []
        for section in key_sections:
            if section in context:
                found_sections.append(section)
        
        print(f"   - Key sections found: {len(found_sections)}/{len(key_sections)}")
        for section in found_sections:
            print(f"     ✅ {section}")
        
        missing_sections = [s for s in key_sections if s not in found_sections]
        if missing_sections:
            print("   - Missing sections:")
            for section in missing_sections:
                print(f"     ❌ {section}")
        
        return len(found_sections) == len(key_sections)
        
    except Exception as e:
        print(f"❌ Error testing market data: {e}")
        return False


def main():
    """Run blockchain comparison tests"""
    print("🔗 Blockchain Data Availability Test")
    print("=" * 60)
    print()
    
    tests = [
        test_comprehensive_market_data,
        test_blockchain_in_vector_chat
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ PASSED\n")
            else:
                print("❌ FAILED\n")
        except Exception as e:
            print(f"❌ FAILED with exception: {e}\n")
    
    print("=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 EXCELLENT! Vector chat has complete blockchain data access!")
        print("\n💡 What this means for users:")
        print("✅ Blockchain developer queries now get comprehensive responses")
        print("✅ Salary information includes all blockchain ranges")
        print("✅ Market trends and forecasts are available")
        print("✅ Technology stack and skills are detailed")
        print("✅ Career guidance is data-driven and accurate")
        print("\n🚀 Vector chat now matches regular chat quality!")
    else:
        print("⚠️ Some blockchain data may still be missing.")
        
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
