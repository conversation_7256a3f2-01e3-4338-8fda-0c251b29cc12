#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Optimized runner for FOIS Chatbot with minimal initialization
This script runs the chatbot without <PERSON>lask's auto-reloader to prevent double initialization
Supports both development and production modes
"""

import os
import sys
from web_app import app, COMPANY_INFO

def run_optimized_server(host='0.0.0.0', port=5001, debug=False, production=False):
    """Run the server with optimized settings"""

    mode = "PRODUCTION" if production else "DEVELOPMENT"
    print(f"🚀 Starting OPTIMIZED FOIS Chatbot Web Interface ({mode})...")
    print(f"🏢 Company: {COMPANY_INFO['FULL_NAME']}")
    print(f"🌐 Access at: http://localhost:{port}")
    print(f"🔧 Debug mode: {'ON' if debug else 'OFF'}")
    print(f"🎯 New Intent System: ACTIVE")
    print(f"⚡ Optimization: NO AUTO-RELOAD (single initialization)")

    if production:
        print("🏭 Production mode: Using Waitress WSGI server")
        print("📋 Features: Thread-safe, production-ready, no debug warnings")
    else:
        print("🛠️ Development mode: Using Flask dev server")
        print("⚠️ Note: Flask dev server warning is normal for development")

    print("=" * 60)

    if production:
        # Use Waitress for production
        try:
            from waitress import serve
            print("🏭 Starting production server with Waitress...")
            serve(app, host=host, port=port, threads=6)
        except ImportError:
            print("⚠️ Waitress not installed. Install with: pip install -r requirements.txt")
            print("   (Waitress is included in requirements.txt for production deployment)")
            print("🔄 Falling back to Flask development server...")
            app.run(host=host, port=port, debug=False, use_reloader=False, threaded=True)
    else:
        # Development mode without reloader
        app.run(
            host=host,
            port=port,
            debug=debug,
            use_reloader=False,  # This prevents the double initialization
            threaded=True
        )

if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description='Optimized FOIS Chatbot Web Interface')
    parser.add_argument('--host', default='0.0.0.0',
                        help='Host to bind to (default: 0.0.0.0)')
    parser.add_argument('--port', type=int, default=5001,
                        help='Port to bind to (default: 5001)')
    parser.add_argument('--debug', action='store_true',
                        help='Enable debug mode (without auto-reload)')
    parser.add_argument('--production', action='store_true',
                        help='Run in production mode with Waitress WSGI server')

    args = parser.parse_args()

    # Production mode overrides debug
    if args.production:
        args.debug = False

    try:
        run_optimized_server(
            host=args.host,
            port=args.port,
            debug=args.debug,
            production=args.production
        )
    except KeyboardInterrupt:
        print("\n👋 Shutting down server...")
    except Exception as e:
        print(f"❌ Error starting server: {e}")
        sys.exit(1)
