#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple test to verify CV data access in vector chat
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_cv_data_access():
    """Test CV data access functionality"""
    
    print("🧪 Testing CV Data Access")
    print("=" * 40)
    
    # Test 1: Check if we can import web_app functions
    print("📦 Test 1: Import web_app functions")
    try:
        from web_app import get_cv_analysis_data, cv_analyses
        print("✅ Successfully imported web_app functions")
        print(f"   - cv_analyses type: {type(cv_analyses)}")
        print(f"   - cv_analyses keys: {list(cv_analyses.keys())}")
    except Exception as e:
        print(f"❌ Failed to import web_app functions: {e}")
        return False
    
    # Test 2: Mock some CV data
    print("\n📝 Test 2: Mock CV data")
    test_user_id = "test_user_cv_123"
    mock_cv_data = {
        'filename': 'test_resume.pdf',
        'upload_time': '2024-01-15 10:30:00',
        'analysis': {
            'success': True,
            'cv_data': {
                'skills': ['Python', 'JavaScript', 'React', 'Node.js'],
                'experience': [
                    {'title': 'Frontend Developer', 'company': 'Tech Corp'}
                ],
                'education': [
                    {'degree': 'Bachelor CS', 'university': 'VNU'}
                ],
                'summary': 'Experienced developer with 3 years in web development'
            },
            'job_matches': [
                {'title': 'Frontend Developer', 'match_score': 85}
            ]
        }
    }
    
    # Store mock data
    cv_analyses[test_user_id] = mock_cv_data
    print(f"✅ Stored mock CV data for user: {test_user_id}")
    
    # Test 3: Test the get_cv_analysis_data function
    print("\n🔍 Test 3: Test get_cv_analysis_data function")
    retrieved_data = get_cv_analysis_data(test_user_id)
    
    if retrieved_data:
        print("✅ Successfully retrieved CV data")
        print(f"   - Filename: {retrieved_data.get('filename')}")
        print(f"   - Has analysis: {bool(retrieved_data.get('analysis'))}")
        if retrieved_data.get('analysis', {}).get('cv_data'):
            skills = retrieved_data['analysis']['cv_data'].get('skills', [])
            print(f"   - Skills found: {len(skills)} - {skills}")
    else:
        print("❌ Failed to retrieve CV data")
        return False
    
    # Test 4: Test vector chatbot CV data access
    print("\n🤖 Test 4: Test vector chatbot CV data access")
    try:
        from vector_chatbot.vector_chatbot_router import VectorChatbotRouter
        
        router = VectorChatbotRouter()
        cv_data = router._get_uploaded_cv_data(test_user_id)
        
        if cv_data:
            print("✅ Vector chatbot successfully accessed CV data")
            print(f"   - Filename: {cv_data.get('filename')}")
            print(f"   - Has analysis: {bool(cv_data.get('analysis'))}")
        else:
            print("❌ Vector chatbot failed to access CV data")
            return False
            
    except Exception as e:
        print(f"❌ Error testing vector chatbot CV access: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # Test 5: Test CV context building
    print("\n📋 Test 5: Test CV context building")
    try:
        from sample_data import SAMPLE_JOBS, JOB_MARKET_INSIGHTS_2024
        
        cv_context = router._build_cv_context_prompt(
            SAMPLE_JOBS,
            JOB_MARKET_INSIGHTS_2024["most_demanded_positions"],
            cv_data
        )
        
        print(f"✅ CV context built successfully")
        print(f"   - Context length: {len(cv_context)} characters")
        
        # Check if CV data is included
        if 'test_resume.pdf' in cv_context:
            print("   ✅ CV filename found in context")
        else:
            print("   ❌ CV filename NOT found in context")
            
        if 'Python' in cv_context:
            print("   ✅ CV skills found in context")
        else:
            print("   ❌ CV skills NOT found in context")
            
        # Show a preview
        preview = cv_context[:300] + "..." if len(cv_context) > 300 else cv_context
        print(f"   📄 Context preview:\n{preview}")
        
    except Exception as e:
        print(f"❌ Error testing CV context building: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print("\n" + "=" * 40)
    print("🎉 All CV data access tests passed!")
    
    # Clean up
    if test_user_id in cv_analyses:
        del cv_analyses[test_user_id]
        print("🧹 Cleaned up test data")
    
    return True


if __name__ == "__main__":
    success = test_cv_data_access()
    if success:
        print("\n✅ CV data access is working correctly!")
        print("💡 The vector chatbot should now be able to access uploaded CV data.")
    else:
        print("\n❌ CV data access has issues that need to be fixed.")
    
    sys.exit(0 if success else 1)
