/* Modern FOIS Chatbot CSS */

/* Reset & Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Colors */
    --primary-color: #ddedff;
    --primary-dark: #e4e4e4;
    --secondary-color: #e9bdbd;
    --accent-color: #f093fb;
    --success-color: #48bb78;
    --warning-color: #ed8936;
    --error-color: #f56565;
    
    /* Neutrals - Improved contrast */
    --white: #ffffff;
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #374151;  /* Darker for better readability */
    --gray-700: #1f2937;  /* Much darker for main text */
    --gray-800: #111827;  /* Very dark for headings */
    --gray-900: #000000;  /* Pure black for maximum contrast */
    
    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 0.15s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* Theme colors - Light Mode (default) */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;
    --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --text-primary: #1e293b;
    --text-secondary: #475569;
    --text-muted: #64748b;
    --border-color: #e2e8f0;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --message-bg: #ffffff;
    --sidebar-bg: #ffffff;
    --input-bg: #f8fafc;

    /* Light mode specific gradients */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --secondary-gradient: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    --accent-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --messages-gradient: linear-gradient(180deg, #f9fafb 0%, #ffffff 100%);
    --welcome-gradient: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
}

/* Dark Mode */
[data-theme="dark"] {
    --bg-primary: #0f222a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-gradient: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    --text-primary: #ffffff;        /* Pure white for maximum contrast */
    --text-secondary: #e2e8f0;      /* Light gray for secondary text */
    --text-muted: #94a3b8;          /* Medium gray for muted text */
    --border-color: #334155;
    --shadow-color: rgba(0, 0, 0, 0.3);
    --message-bg: #1e293b;
    --sidebar-bg: #0f172a;
    --input-bg: #334155;

    /* Dark mode specific gradients */
    --primary-gradient: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    --secondary-gradient: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    --accent-gradient: linear-gradient(135deg, #0f766e 0%, #059669 100%);
    --messages-gradient: linear-gradient(180deg, #0f222a 0%, #1e293b 100%);
    --welcome-gradient: linear-gradient(135deg, #1e293b 0%, #334155 100%);

    /* Override gray colors for dark mode */
    --gray-50: #1e293b;
    --gray-100: #334155;
    --gray-200: #475569;
    --gray-300: #64748b;
    --gray-400: #94a3b8;
    --gray-500: #cbd5e1;
    --gray-600: #e2e8f0;
    --gray-700: #f1f5f9;
    --gray-800: #f8fafc;
    --gray-900: #ffffff;
}

/* Dark Mode Component Fixes */

/* 1. Sidebar Header - Fix background and text */
[data-theme="dark"] .sidebar-header {
    background: var(--sidebar-bg);
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .logo-text h2 {
    color: var(--text-primary);
}

[data-theme="dark"] .logo-text span {
    color: var(--text-secondary);
}

/* 2. Language Current - Fix visibility */
[data-theme="dark"] .lang-current {
    background: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .lang-current:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

/* 3. Chat Header - Fix background and text */
[data-theme="dark"] .chat-header {
    background: var(--bg-primary);
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
}

[data-theme="dark"] .header-info .info h3 {
    color: var(--text-primary);
}

[data-theme="dark"] .header-info .info .status {
    color: var(--text-secondary);
}

/* 4. Messages Container - Fix background and text */
[data-theme="dark"] .messages-container {
    background: var(--bg-primary);
    color: var(--text-primary);
}

[data-theme="dark"] .message-bubble {
    background: var(--message-bg);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .bot-message .message-bubble {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

[data-theme="dark"] .user-message .message-bubble {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* 5. Suggestions Container - Fix background and text */
[data-theme="dark"] .suggestions-container {
    background: var(--bg-primary);
    color: var(--text-primary);
}

[data-theme="dark"] .suggestions-header {
    color: var(--text-primary);
}

[data-theme="dark"] .suggestion-item {
    background: var(--bg-secondary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .suggestion-item:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* 6. Input Container - Fix background and text */
[data-theme="dark"] .input-container {
    background: var(--bg-primary);
    border-top: 1px solid var(--border-color);
}

[data-theme="dark"] .input-field {
    background: var(--input-bg);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] #messageInput {
    color: var(--text-primary);
    background: transparent;
}

[data-theme="dark"] #messageInput::placeholder {
    color: var(--text-muted);
}

/* Additional Dark Mode Fixes */

/* Company details and sidebar content */
[data-theme="dark"] .company-header h3 {
    color: var(--text-primary);
}

[data-theme="dark"] .detail-item {
    color: var(--text-secondary);
}

[data-theme="dark"] .detail-item i {
    color: var(--text-muted);
}

/* Quick actions */
[data-theme="dark"] .quick-actions h4 {
    color: var(--text-primary);
}

[data-theme="dark"] .action-item {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .action-item:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Welcome message */
[data-theme="dark"] .welcome-header h3 {
    color: var(--text-primary);
}

[data-theme="dark"] .welcome-text {
    color: var(--text-secondary);
}

[data-theme="dark"] .feature-item {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    border: 1px solid var(--border-color);
}

[data-theme="dark"] .feature-item:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Buttons and icons */
[data-theme="dark"] .btn-icon {
    color: var(--text-secondary);
    background: transparent;
}

[data-theme="dark"] .btn-icon:hover {
    background: var(--bg-tertiary);
    color: var(--text-primary);
}

/* Input actions */
[data-theme="dark"] .input-actions .btn-icon {
    color: var(--text-secondary);
}

[data-theme="dark"] .input-actions .btn-icon:hover {
    background: var(--bg-tertiary);
    color: var(--primary-color);
}

/* Powered by text */
[data-theme="dark"] .powered-by {
    color: var(--text-muted);
}

/* Typing indicator */
[data-theme="dark"] .typing-indicator {
    background: var(--bg-primary);
    color: var(--text-secondary);
}

/* COMPREHENSIVE DARK MODE TEXT FIXES - ENSURE ALL TEXT IS WHITE */

/* Override all gray color references in dark mode */
[data-theme="dark"] .logo-text h2,
[data-theme="dark"] .company-header h3,
[data-theme="dark"] .quick-actions h4,
[data-theme="dark"] .header-info .info h3,
[data-theme="dark"] .welcome-header h3,
[data-theme="dark"] h1, [data-theme="dark"] h2, [data-theme="dark"] h3,
[data-theme="dark"] h4, [data-theme="dark"] h5, [data-theme="dark"] h6 {
    color: var(--text-primary) !important;  /* Force white text */
}

[data-theme="dark"] .logo-text span,
[data-theme="dark"] .detail-item,
[data-theme="dark"] .action-item,
[data-theme="dark"] .feature-item,
[data-theme="dark"] .welcome-text,
[data-theme="dark"] .suggestions-header,
[data-theme="dark"] .suggestion-item,
[data-theme="dark"] .status,
[data-theme="dark"] p, [data-theme="dark"] span, [data-theme="dark"] div {
    color: var(--text-primary) !important;  /* Force white text */
}

/* Sidebar elements */
[data-theme="dark"] .sidebar-toggle {
    color: var(--text-secondary) !important;
}

[data-theme="dark"] .sidebar-toggle:hover {
    color: var(--text-primary) !important;
}

/* Button elements */
[data-theme="dark"] .btn-secondary {
    color: var(--text-primary) !important;
    background: var(--bg-tertiary) !important;
}

[data-theme="dark"] .btn-secondary:hover {
    color: var(--text-primary) !important;
    background: var(--bg-secondary) !important;
}

[data-theme="dark"] .btn-secondary:disabled {
    color: var(--text-muted) !important;
}

[data-theme="dark"] .mobile-menu-btn {
    color: var(--text-secondary) !important;
}

[data-theme="dark"] .mobile-menu-btn:hover {
    color: var(--text-primary) !important;
}

/* Icon buttons */
[data-theme="dark"] .btn-attach {
    color: var(--text-secondary) !important;
}

[data-theme="dark"] .btn-attach:hover {
    color: var(--text-primary) !important;
}

/* Message bubbles - theme-appropriate backgrounds and text */
[data-theme="light"] .message-bubble {
    background: var(--secondary-gradient) !important;  /* Light gradient in light mode */
    color: var(--gray-900) !important;  /* Dark text on light gradient */
}

[data-theme="dark"] .message-bubble {
    background: var(--secondary-gradient) !important;  /* Dark gradient in dark mode */
    color: var(--text-primary) !important;  /* White text on dark gradient */
}

/* User messages always use primary gradient with white text */
[data-theme="light"] .user-message .message-bubble,
[data-theme="dark"] .user-message .message-bubble {
    background: var(--primary-gradient) !important;  /* Primary gradient in both modes */
    color: var(--white) !important;  /* White text on colored gradient */
}

/* Input placeholder */
[data-theme="dark"] #messageInput::placeholder {
    color: var(--text-muted) !important;
}

/* Upload modal elements */
[data-theme="dark"] .upload-text h4,
[data-theme="dark"] .result-header h4,
[data-theme="dark"] .job-title {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .upload-text p,
[data-theme="dark"] .progress-text,
[data-theme="dark"] .job-company {
    color: var(--text-secondary) !important;
}

/* Token usage elements */
[data-theme="dark"] .token-usage-header span,
[data-theme="dark"] .token-detail-value {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .token-label,
[data-theme="dark"] .token-detail-label,
[data-theme="dark"] .btn-icon-small {
    color: var(--text-secondary) !important;
}

[data-theme="dark"] .btn-icon-small:hover {
    color: var(--text-primary) !important;
}

/* Ensure all interactive elements have proper hover states */
[data-theme="dark"] .action-item:hover,
[data-theme="dark"] .feature-item:hover,
[data-theme="dark"] .suggestion-item:hover,
[data-theme="dark"] button:hover,
[data-theme="dark"] .btn:hover {
    color: var(--text-primary) !important;
}

/* Status indicators */
[data-theme="dark"] .status.online {
    color: var(--success-color) !important;
}

/* Force white text for any remaining elements */
[data-theme="dark"] * {
    border-color: var(--border-color);
}

[data-theme="dark"] body,
[data-theme="dark"] .chat-container,
[data-theme="dark"] .main-content {
    color: var(--text-primary);
}

/* Company Card Dark Mode Fix */
[data-theme="dark"] .company-card {
    background: var(--bg-secondary) !important;
    border: 1px solid var(--border-color) !important;
    box-shadow: var(--shadow-color) 0 4px 12px !important;
}

[data-theme="dark"] .company-header h3 {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .company-header i {
    color: var(--primary-color) !important;
}

/* Company Details Dark Mode Fix */
[data-theme="dark"] .company-details {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .company-details .detail-item {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .company-details .detail-item i {
    color: var(--text-secondary) !important;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: var(--text-primary);
    background: var(--bg-gradient);
    overflow: hidden;
    height: 100vh;
    font-weight: 400;
    transition: var(--transition-normal);
}

/* Background */
.background {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.bg-gradient {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    opacity: 0.05;
}

.bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(102, 126, 234, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(118, 75, 162, 0.1) 0%, transparent 50%);
    background-size: 100px 100px;
}

/* App Container */
.app-container {
    display: flex;
    height: 100vh;
    background: var(--bg-primary);
    border-radius: var(--radius-2xl);
    margin: var(--space-md);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    transition: var(--transition-normal);
}

/* Sidebar */
.sidebar {
    width: 320px;
    background: var(--sidebar-bg);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    transition: var(--transition-normal);
}

.sidebar-header {
    padding: var(--space-xl);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.logo-icon {
    width: 48px;
    height: 48px;
    background: var(--primary-gradient);  /* Use theme-appropriate gradient */
    border-radius: var(--radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);  /* Always white text on gradient */
    font-size: 20px;
    box-shadow: var(--shadow-md);
}

.logo-text h2 {
    font-size: 20px;
    font-weight: 700;
    color: var(--gray-900);  /* Darker for better contrast */
    margin-bottom: 2px;
}

.logo-text span {
    font-size: 12px;
    color: var(--gray-600);  /* Darker for better readability */
    font-weight: 500;
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--gray-500);
    font-size: 18px;
    cursor: pointer;
    padding: var(--space-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.sidebar-toggle:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.sidebar-content {
    flex: 1;
    padding: var(--space-xl);
    display: flex;
    flex-direction: column;
    gap: var(--space-xl);
}

/* Company Card */
.company-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--gray-200);
}

.company-header {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    margin-bottom: var(--space-md);
}

.company-header i {
    color: var(--primary-color);
    font-size: 16px;
}

.company-header h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-900);  /* Darker for better contrast */
}

.company-details {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm);
}

.detail-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    font-size: 13px;
    color: var(--gray-700);  /* Darker for better readability */
    font-weight: 500;  /* Slightly bolder */
}

.detail-item i {
    width: 16px;
    color: var(--gray-400);
    font-size: 12px;
}

/* Quick Actions */
.quick-actions h4 {
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-900);  /* Darker for better contrast */
    margin-bottom: var(--space-md);
}

.action-list {
    display: flex;
    flex-direction: column;
    gap: var(--space-xs);
}

.action-item {
    display: flex;
    align-items: center;
    gap: var(--space-md);
    padding: var(--space-md);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-fast);
    font-size: 13px;
    color: var(--gray-700);  /* Darker for better readability */
    font-weight: 500;  /* Slightly bolder */
}

.action-item:hover {
    background: var(--gray-100);
    color: var(--gray-900);  /* Much darker on hover */
}

.action-item i {
    width: 16px;
    color: var(--primary-color);
    font-size: 14px;
}

/* Sidebar Footer */
.sidebar-footer {
    margin-top: auto;
    padding-top: var(--space-lg);
    border-top: 1px solid var(--gray-200);
}

.btn-secondary {
    width: 100%;
    padding: var(--space-md);
    background: var(--gray-100);
    border: none;
    border-radius: var(--radius-lg);
    color: var(--gray-600);
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm);
}

.btn-secondary:hover {
    background: var(--gray-200);
    color: var(--gray-700);
}

.btn-secondary:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary:disabled:hover {
    background: var(--gray-100);
    color: var(--gray-500);
    transform: none;
}

/* Chat Container */
.chat-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--bg-primary);
}

/* Chat Header */
.chat-header {
    padding: var(--space-xl);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: var(--white);
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    color: var(--gray-500);
    font-size: 18px;
    cursor: pointer;
    padding: var(--space-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.mobile-menu-btn:hover {
    background: var(--gray-100);
    color: var(--gray-700);
}

.header-info {
    display: flex;
    align-items: center;
    gap: var(--space-md);
}

.header-info .avatar {
    width: 40px;
    height: 40px;
    background: var(--primary-gradient);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 16px;
    box-shadow: var(--shadow-md);
}

.header-info .info h3 {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-900);  /* Darker for better contrast */
    margin-bottom: 2px;
}

.status {
    display: flex;
    align-items: center;
    gap: var(--space-xs);
    font-size: 12px;
    color: var(--gray-500);
}

.status.online {
    color: var(--success-color);
}

.status i {
    font-size: 8px;
}

.header-actions {
    display: flex;
    gap: var(--space-sm);
}

.btn-icon {
    width: 36px;
    height: 36px;
    background: var(--gray-100);
    border: none;
    border-radius: var(--radius-lg);
    color: var(--gray-500);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.btn-icon:hover {
    background: var(--gray-200);
    color: var(--gray-700);
}

/* Messages Container */
.messages-container {
    flex: 1;
    overflow-y: auto;
    padding: var(--space-xl);
    background: var(--messages-gradient);
    scroll-behavior: smooth;
}

.message-group {
    margin-bottom: var(--space-xl);
}

.message {
    display: flex;
    gap: var(--space-md);
    margin-bottom: var(--space-lg);
    animation: fadeInUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transition: all 0.3s ease;
}

.message.user-message {
    flex-direction: row-reverse;
}

.message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    flex-shrink: 0;
    margin-top: var(--space-xs);
}

.bot-message .message-avatar {
    background: var(--primary-gradient);
    color: var(--white);
}

.user-message .message-avatar {
    background: var(--accent-gradient);
    color: var(--white);
}

.message-content {
    flex: 1;
    max-width: 70%;
}

.message-bubble {
    background: var(--secondary-gradient);  /* Use theme-appropriate gradient */
    border-radius: var(--radius-xl);
    padding: var(--space-lg);
    box-shadow: var(--shadow-sm);
    border: 1px solid var(--border-color);
    position: relative;
    color: var(--text-primary);  /* Use theme-appropriate text color */
    line-height: 1.6;  /* Better line height for readability */
}

.user-message .message-bubble {
    background: var(--primary-gradient);
    color: var(--white);
    border: none;
}

.message-bubble::before {
    content: '';
    position: absolute;
    top: var(--space-md);
    width: 0;
    height: 0;
    border: 6px solid transparent;
}

.bot-message .message-bubble::before {
    left: -12px;
    border-right-color: var(--white);
}

.user-message .message-bubble::before {
    right: -12px;
    border-left-color: var(--primary-color);
}

/* Welcome Message */
.welcome-message .message-bubble {
    background: var(--welcome-gradient);
    border: 2px solid var(--primary-color);
    border-radius: var(--radius-2xl);
    padding: var(--space-2xl);
}

.welcome-header h3 {
    font-size: 18px;
    font-weight: 700;
    color: var(--gray-900);  /* Much darker for better contrast */
    margin-bottom: var(--space-lg);
    text-align: center;
}

.welcome-features {
    margin: var(--space-xl) 0;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-md);
}

.feature-item {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    padding: var(--space-md);
    background: var(--white);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
    font-size: 13px;
    color: var(--gray-700);  /* Darker for better readability */
    font-weight: 500;  /* Slightly bolder */
    transition: var(--transition-fast);
    cursor: pointer;
    user-select: none;
}

.feature-item:hover {
    border-color: var(--primary-color);
    color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.feature-item i {
    color: var(--primary-color);
    font-size: 16px;
}

.welcome-text {
    text-align: center;
    color: var(--gray-700);  /* Darker for better readability */
    font-size: 14px;
    font-weight: 500;  /* Slightly bolder */
    margin-top: var(--space-lg);
}

/* Suggestions */
.suggestions-container {
    padding: 0 var(--space-xl) var(--space-lg);
    background: var(--white);
}

.suggestions-header {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    margin-bottom: var(--space-md);
    font-size: 13px;
    font-weight: 600;  /* Bolder for better visibility */
    color: var(--gray-700);  /* Darker for better readability */
}

.suggestions-header i {
    color: var(--warning-color);
    font-size: 14px;
}

.suggestions-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--space-sm);
}

.suggestion-item {
    background: var(--gray-100);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-xl);
    padding: var(--space-sm) var(--space-md);
    font-size: 12px;
    color: var(--gray-700);  /* Darker for better readability */
    font-weight: 500;  /* Slightly bolder */
    cursor: pointer;
    transition: var(--transition-fast);
    white-space: nowrap;
}

.suggestion-item:hover {
    background: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Input Container */
.input-container {
    padding: var(--space-xl);
    background: var(--white);
    border-top: 1px solid var(--gray-200);
}

.input-wrapper {
    background: var(--gray-50);
    border-radius: var(--radius-2xl);
    border: 2px solid var(--gray-200);
    transition: var(--transition-fast);
    overflow: hidden;
}

.input-wrapper:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.input-field {
    display: flex;
    align-items: center;  /* Center alignment for single line */
    gap: var(--space-sm);
    padding: var(--space-md);
}

#messageInput {
    flex: 1;
    border: none;
    background: none;
    outline: none;
    resize: none;
    font-family: inherit;
    font-size: 14px;
    line-height: 34px;  /* Match average button height for single line */
    color: var(--gray-900);  /* Much darker for better contrast */
    font-weight: 500;  /* Slightly bolder for input text */
    min-height: 34px;  /* Match line-height for consistent sizing */
    max-height: 120px;
    padding: 0;  /* Remove padding */
}

/* When textarea has multiple lines, adjust line-height and alignment */
.input-field.multiline #messageInput {
    line-height: 1.5;  /* Normal line-height for multi-line */
    min-height: 20px;  /* Smaller min-height for multi-line */
}

.input-field.multiline {
    align-items: flex-start;  /* Top align for multi-line */
}

#messageInput::placeholder {
    color: var(--gray-500);  /* Darker placeholder for better visibility */
    font-weight: 400;  /* Normal weight for placeholder */
}

.input-actions {
    display: flex;
    gap: var(--space-xs);
    align-items: center;  /* Center alignment for buttons */
}

.btn-attach {
    width: 32px;
    height: 32px;
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    border-radius: var(--radius-lg);
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
}

.btn-attach:hover {
    background: var(--gray-200);
    color: var(--gray-600);
}

.btn-send {
    width: 36px;
    height: 36px;
    background: var(--primary-gradient);
    border: none;
    border-radius: var(--radius-lg);
    color: var(--white);
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    box-shadow: var(--shadow-sm);
}

.btn-send:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.btn-send:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
}

.input-footer {
    text-align: center;
    margin-top: var(--space-md);
}

.powered-by {
    font-size: 11px;
    color: var(--gray-500);  /* Darker for better readability */
    font-weight: 500;  /* Slightly bolder */
}

/* Typing Indicator */
.typing-indicator {
    padding: var(--space-xl);  /* Match messages container padding */
    background: var(--messages-gradient);  /* Match messages container background */
    animation: fadeInUp 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    transition: all 0.3s ease;
    max-height: 200px;  /* Allow space for typing indicator */
    overflow: hidden;  /* Hide overflow during transitions */
}

.typing-indicator.hidden {
    opacity: 0;
    transform: translateY(-10px);  /* Slide up when hiding since it's above input */
    pointer-events: none;
    transition: all 0.3s ease;
    max-height: 0;  /* Collapse height when hidden */
    padding: 0;  /* Remove all padding when hidden */
}

.typing-bubble {
    background: var(--white);
    border-radius: var(--radius-xl);
    padding: var(--space-md) var(--space-lg);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid var(--gray-200);
    display: inline-block;
    position: relative;
    overflow: hidden;
}

.typing-bubble::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(79, 70, 229, 0.1), transparent);
    animation: typing-shimmer 2s infinite;
}

.typing-dots {
    display: flex;
    gap: var(--space-xs);
    align-items: center;
    position: relative;
    z-index: 1;
}

.typing-dots span {
    width: 8px;
    height: 8px;
    background: var(--primary-color);
    border-radius: 50%;
    animation: typing-bounce 1.6s infinite ease-in-out;
    transform-origin: center;
}

.typing-dots span:nth-child(1) {
    animation-delay: 0s;
}

.typing-dots span:nth-child(2) {
    animation-delay: 0.2s;
}

.typing-dots span:nth-child(3) {
    animation-delay: 0.4s;
}

/* Scrollbar */
.messages-container::-webkit-scrollbar {
    width: 6px;
}

.messages-container::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes typing-bounce {
    0%, 60%, 100% {
        transform: translateY(0) scale(1);
        opacity: 0.7;
    }
    30% {
        transform: translateY(-12px) scale(1.1);
        opacity: 1;
    }
}

@keyframes typing-shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Thinking message styles */
.thinking-message .message-bubble {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
    border: 1px solid #4338ca;
    color: #ffffff;
    font-weight: 500;
    position: relative;
    animation: thinking-glow 3s infinite ease-in-out;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Removed cloud emoji - user doesn't like it */

@keyframes thinking-glow {
    0%, 100% {
        box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3), 0 0 0 0 rgba(79, 70, 229, 0.4);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 6px 20px rgba(79, 70, 229, 0.4), 0 0 20px 5px rgba(79, 70, 229, 0.2);
        transform: scale(1.01);
    }
}

@keyframes thinking-float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-4px) rotate(5deg);
    }
    66% {
        transform: translateY(-2px) rotate(-3deg);
    }
}

/* Additional thinking message effects */
.thinking-message {
    animation: fadeInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.thinking-message .message-bubble {
    position: relative;
    overflow: hidden;
}

.thinking-message .message-bubble::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
    animation: thinking-shimmer 2.5s infinite ease-in-out;
    border-radius: inherit;
    pointer-events: none;
}

@keyframes thinking-shimmer {
    0% {
        left: -100%;
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        left: 100%;
        opacity: 0;
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes slideOutLeft {
    from {
        transform: translateX(0);
    }
    to {
        transform: translateX(-100%);
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .app-container {
        margin: var(--space-sm);
        border-radius: var(--radius-xl);
    }

    .sidebar {
        width: 280px;
    }

    .feature-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .app-container {
        margin: 0;
        border-radius: 0;
        height: 100vh;
    }

    .sidebar {
        position: fixed;
        top: 0;
        left: 0;
        height: 100vh;
        z-index: 1000;
        transform: translateX(-100%);
        transition: transform var(--transition-normal);
    }

    .sidebar.open {
        transform: translateX(0);
        animation: slideInLeft var(--transition-normal);
    }

    .sidebar-toggle {
        display: block;
    }

    .mobile-menu-btn {
        display: block;
    }

    .chat-header {
        padding: var(--space-lg);
    }

    .messages-container {
        padding: var(--space-lg);
    }

    .typing-indicator {
        padding: var(--space-lg);  /* Match messages container on tablet */
    }

    .input-container {
        padding: var(--space-lg);
    }

    .suggestions-container {
        padding: 0 var(--space-lg) var(--space-md);
    }

    .message-content {
        max-width: 85%;
    }

    .welcome-message .message-bubble {
        padding: var(--space-lg);
    }

    .welcome-header h3 {
        font-size: 16px;
    }
}

@media (max-width: 480px) {
    .sidebar {
        width: 100%;
    }

    .chat-header {
        padding: var(--space-md);
    }

    .messages-container {
        padding: var(--space-md);
    }

    .typing-indicator {
        padding: var(--space-md);  /* Match messages container on mobile */
    }

    .input-container {
        padding: var(--space-md);
    }

    .suggestions-container {
        padding: 0 var(--space-md) var(--space-sm);
    }

    .message-content {
        max-width: 90%;
    }

    .suggestions-list {
        flex-direction: column;
    }

    .suggestion-item {
        text-align: center;
        white-space: normal;
    }

    .feature-grid {
        gap: var(--space-sm);
    }

    .feature-item {
        padding: var(--space-sm);
        font-size: 12px;
    }
}

/* Theme Toggle Button Styling */
#themeToggle {
    transition: all var(--transition-normal);
}

#themeToggle:hover {
    transform: scale(1.1);
}

#themeToggle i {
    transition: all var(--transition-normal);
}

/* Dark theme specific styles for theme toggle */
[data-theme="dark"] #themeToggle {
    color: var(--text-primary);
}

[data-theme="dark"] #themeToggle:hover {
    background: var(--bg-tertiary);
    color: var(--primary-color);
}

/* Light theme specific styles for theme toggle */
[data-theme="light"] #themeToggle {
    color: var(--text-primary);
}

[data-theme="light"] #themeToggle:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Text Readability Improvements */
.text-dark {
    color: var(--gray-900) !important;
    font-weight: 600 !important;
}

.text-readable {
    color: var(--gray-800) !important;
    font-weight: 500 !important;
}

/* Better contrast for all text elements */
h1, h2, h3, h4, h5, h6 {
    color: var(--gray-900);
    font-weight: 700;
}

p, span, div {
    color: var(--gray-800);
}

/* Ensure good contrast for interactive elements */
button, .btn, .action-item, .suggestion-item {
    font-weight: 500;
}

button:hover, .btn:hover, .action-item:hover, .suggestion-item:hover {
    color: var(--gray-900) !important;
    font-weight: 600;
}

/* Focus States */
.btn-send:focus,
.btn-attach:focus,
.btn-icon:focus,
.btn-secondary:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

#messageInput:focus {
    outline: none;
}

/* CV Upload Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.modal.hidden {
    display: none;
}

.modal-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    position: relative;
    background: var(--white);
    border-radius: var(--radius-2xl);
    box-shadow: var(--shadow-xl);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    animation: slideInUp 0.3s ease;
}

.modal-header {
    padding: var(--space-xl);
    border-bottom: 1px solid var(--gray-200);
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
}

.modal-header h3 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--space-sm);
}

.modal-close {
    background: none;
    border: none;
    color: var(--white);
    font-size: 18px;
    cursor: pointer;
    padding: var(--space-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
}

.modal-body {
    padding: var(--space-xl);
    max-height: 60vh;
    overflow-y: auto;
}

.modal-footer {
    padding: var(--space-xl);
    border-top: 1px solid var(--gray-200);
    display: flex;
    gap: var(--space-md);
    justify-content: flex-end;
    background: var(--gray-50);
}

/* Upload Area */
.upload-area {
    border: 2px dashed var(--gray-300);
    border-radius: var(--radius-xl);
    padding: var(--space-2xl);
    text-align: center;
    transition: var(--transition-fast);
    cursor: pointer;
}

.upload-area:hover {
    border-color: var(--primary-color);
    background: var(--gray-50);
}

.upload-area.dragover {
    border-color: var(--primary-color);
    background: rgba(102, 126, 234, 0.1);
}

.upload-icon {
    font-size: 48px;
    color: var(--primary-color);
    margin-bottom: var(--space-lg);
}

.upload-text h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--space-sm);
}

.upload-text p {
    font-size: 14px;
    color: var(--gray-600);
    margin-bottom: var(--space-lg);
}

.btn-upload {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--white);
    border: none;
    padding: var(--space-md) var(--space-xl);
    border-radius: var(--radius-lg);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-fast);
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    margin: 0 auto;
}

.btn-upload:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

/* Progress Bar */
.upload-progress {
    margin-top: var(--space-lg);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--gray-200);
    border-radius: var(--radius-md);
    overflow: hidden;
    margin-bottom: var(--space-sm);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--radius-md);
    transition: width 0.3s ease;
    width: 0%;
}

.progress-text {
    text-align: center;
    font-size: 14px;
    color: var(--gray-600);
    font-weight: 500;
}

/* Upload Result */
.upload-result {
    margin-top: var(--space-lg);
    padding: var(--space-lg);
    background: var(--gray-50);
    border-radius: var(--radius-lg);
    border: 1px solid var(--gray-200);
}

.result-header {
    display: flex;
    align-items: center;
    gap: var(--space-sm);
    margin-bottom: var(--space-md);
}

.result-header h4 {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-800);
}

.job-match {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--space-md);
    margin-bottom: var(--space-sm);
    border: 1px solid var(--gray-200);
    transition: var(--transition-fast);
}

.job-match:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.job-title {
    font-size: 14px;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: var(--space-xs);
}

.job-company {
    font-size: 12px;
    color: var(--gray-600);
    margin-bottom: var(--space-sm);
}

.match-score {
    display: inline-block;
    background: var(--success-color);
    color: var(--white);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-md);
    font-size: 11px;
    font-weight: 600;
}

.match-score.medium {
    background: var(--warning-color);
}

.match-score.low {
    background: var(--gray-400);
}

/* Modal Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .message-bubble {
        border-width: 2px;
        color: var(--gray-900) !important;
    }

    .btn-send,
    .logo-icon,
    .header-info .avatar {
        border: 2px solid var(--white);
    }

    /* Force darker text in high contrast mode */
    body, .detail-item, .action-item, .feature-item,
    .suggestion-item, .welcome-text, .suggestions-header {
        color: var(--gray-900) !important;
        font-weight: 600 !important;
    }
}

/* Token Usage Styles */
.token-usage-container {
    background: var(--white);
    border: 1px solid var(--gray-200);
    border-radius: var(--radius-lg);
    margin-top: var(--space-sm);
    padding: var(--space-md);
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.token-usage-container.hidden {
    display: none;
}

.token-usage-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-md);
    padding-bottom: var(--space-sm);
    border-bottom: 1px solid var(--gray-200);
}

.token-usage-header i {
    color: var(--primary-color);
    margin-right: var(--space-sm);
}

.token-usage-header span {
    font-weight: 600;
    color: var(--gray-700);
    font-size: 0.9rem;
}

.btn-icon-small {
    background: none;
    border: none;
    padding: var(--space-xs);
    border-radius: var(--radius-sm);
    color: var(--gray-500);
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-icon-small:hover {
    background: var(--gray-100);
    color: var(--primary-color);
}

.token-usage-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-md);
    margin-bottom: var(--space-md);
}

.token-stat {
    text-align: center;
    padding: var(--space-sm);
    background: var(--gray-50);
    border-radius: var(--radius-md);
    border: 1px solid var(--gray-200);
}

.token-label {
    font-size: 0.75rem;
    color: var(--gray-500);
    margin-bottom: var(--space-xs);
    font-weight: 500;
}

.token-value {
    font-size: 1.1rem;
    font-weight: 700;
    color: var(--primary-color);
}

.token-usage-details {
    display: flex;
    justify-content: space-between;
    padding-top: var(--space-sm);
    border-top: 1px solid var(--gray-200);
}

.token-detail-label {
    font-size: 0.8rem;
    color: var(--gray-500);
    margin-right: var(--space-xs);
}

.token-detail-value {
    font-size: 0.8rem;
    font-weight: 600;
    color: var(--gray-700);
}

/* Dark mode token usage */
[data-theme="dark"] .token-usage-container {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    color: var(--text-primary);
}

[data-theme="dark"] .token-usage-header {
    border-color: var(--border-color);
}

[data-theme="dark"] .token-usage-header span {
    color: var(--text-primary);
}

[data-theme="dark"] .token-usage-header i {
    color: var(--primary-color);
}

[data-theme="dark"] .token-stat {
    background: var(--bg-tertiary);
    border-color: var(--border-color);
}

[data-theme="dark"] .token-label {
    color: var(--text-secondary);
}

[data-theme="dark"] .token-value {
    color: var(--primary-color);
}

[data-theme="dark"] .token-detail-label {
    color: var(--text-secondary);
}

[data-theme="dark"] .token-detail-value {
    color: var(--text-primary);
}

[data-theme="dark"] .btn-icon-small {
    color: var(--text-secondary);
}

[data-theme="dark"] .btn-icon-small:hover {
    background: var(--bg-tertiary);
    color: var(--primary-color);
}

/* Responsive token usage */
@media (max-width: 768px) {
    .token-usage-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: var(--space-sm);
    }

    .token-usage-details {
        flex-direction: column;
        gap: var(--space-xs);
    }

    .token-usage-container {
        padding: var(--space-sm);
    }
}

/* Token toggle button active state */
.btn-icon.token-active {
    background: var(--primary-color);
    color: var(--white);
}

.btn-icon.token-active:hover {
    background: var(--primary-dark);
}
