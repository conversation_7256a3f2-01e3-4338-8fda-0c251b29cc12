{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "🚀 Launch FOIS Chatbot (Port 5001)",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/web_app.py",
            "console": "integratedTerminal",
            "env": {
                "FLASK_ENV": "development",
                "FLASK_DEBUG": "0",
                "PYTHONPATH": "${workspaceFolder}"
            },
            "args": ["--host=0.0.0.0", "--port=5001", "--no-reload"],
            "cwd": "${workspaceFolder}",
            "stopOnEntry": false,
            "autoReload": {
                "enable": false
            }
        },
        {
            "name": "🚀 Launch FOIS Chatbot (Safe Mode)",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/web_app.py",
            "console": "integratedTerminal",
            "env": {
                "FLASK_ENV": "production",
                "FLASK_DEBUG": "0",
                "PYTHONPATH": "${workspaceFolder}"
            },
            "args": [],
            "cwd": "${workspaceFolder}",
            "stopOnEntry": false,
            "justMyCode": false
        },
        {
            "name": "🌐 Debug Frontend (Chrome)",
            "type": "chrome",
            "request": "launch",
            "url": "http://localhost:5001/modern",
            "webRoot": "${workspaceFolder}/static",
            "sourceMaps": true,
            "userDataDir": "${workspaceFolder}/.vscode/chrome-debug-profile",
            "runtimeArgs": [
                "--disable-web-security",
                "--disable-features=VizDisplayCompositor"
            ]
        },
        {
            "name": "🔗 Debug Frontend (Attach to Chrome)",
            "type": "chrome",
            "request": "attach",
            "port": 9222,
            "webRoot": "${workspaceFolder}/static",
            "sourceMaps": true
        },
        {
            "name": "📧 Run Email Bot",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/main.py",
            "console": "integratedTerminal",
            "env": {
                "PYTHONPATH": "${workspaceFolder}"
            },
            "args": [],
            "cwd": "${workspaceFolder}",
            "stopOnEntry": false,
            "justMyCode": false
        },
    ],
    "compounds": [
        {
            "name": "🔥 Debug Full Stack (Backend + Frontend)",
            "configurations": [
                "🚀 Launch FOIS Chatbot (Safe Mode)",
                "🌐 Debug Frontend (Chrome)"
            ],
            "stopAll": true,
            "presentation": {
                "hidden": false,
                "group": "fullstack",
                "order": 1
            }
        },
        {
            "name": "🔥 Debug Full Stack (Development Mode)",
            "configurations": [
                "🚀 Launch FOIS Chatbot (Port 5001)",
                "🌐 Debug Frontend (Chrome)"
            ],
            "stopAll": true,
            "presentation": {
                "hidden": false,
                "group": "fullstack",
                "order": 2
            }
        },
      
    ] 
}