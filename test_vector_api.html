<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vector API Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        .input-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        textarea {
            height: 60px;
            resize: vertical;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .result.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .result.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .api-toggle {
            margin-bottom: 20px;
            padding: 15px;
            background: #e9ecef;
            border-radius: 5px;
        }
        .toggle-button {
            background: #28a745;
            margin-right: 10px;
        }
        .toggle-button.regular {
            background: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Vector API Test Page</h1>
        
        <div class="api-toggle">
            <h3>API Mode Selection</h3>
            <button id="vectorToggle" class="toggle-button">Vector API</button>
            <button id="regularToggle" class="toggle-button regular">Regular API</button>
            <p>Current API: <strong id="currentAPI">Vector API (/api/chat-vector)</strong></p>
        </div>

        <div class="test-section">
            <h3>🧪 Chat Message Test</h3>
            <div class="input-group">
                <label for="testMessage">Test Message:</label>
                <textarea id="testMessage" placeholder="Enter your test message here...">Hello, I want to know about job opportunities</textarea>
            </div>
            <button id="sendMessage">Send Message</button>
            <div id="chatResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔍 Vector Analysis Test</h3>
            <div class="input-group">
                <label for="analysisMessage">Message for Analysis:</label>
                <textarea id="analysisMessage" placeholder="Enter message to analyze...">I want to upload my CV for review</textarea>
            </div>
            <button id="analyzeMessage">Analyze Intent</button>
            <div id="analysisResult" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 Comparison Test</h3>
            <div class="input-group">
                <label for="compareMessage">Message to Compare:</label>
                <textarea id="compareMessage" placeholder="Enter message to compare both APIs...">What is the salary range for Python developers?</textarea>
            </div>
            <button id="compareAPIs">Compare Both APIs</button>
            <div id="comparisonResult" class="result" style="display: none;"></div>
        </div>
    </div>

    <script>
        let useVectorAPI = true;
        
        // API endpoints
        const vectorAPI = '/api/chat-vector';
        const regularAPI = '/api/chat';
        const analysisAPI = '/api/vector-analysis';
        
        // DOM elements
        const vectorToggle = document.getElementById('vectorToggle');
        const regularToggle = document.getElementById('regularToggle');
        const currentAPIDisplay = document.getElementById('currentAPI');
        
        // Initialize
        updateAPIDisplay();
        
        // Event listeners
        vectorToggle.addEventListener('click', () => {
            useVectorAPI = true;
            updateAPIDisplay();
        });
        
        regularToggle.addEventListener('click', () => {
            useVectorAPI = false;
            updateAPIDisplay();
        });
        
        document.getElementById('sendMessage').addEventListener('click', testChatMessage);
        document.getElementById('analyzeMessage').addEventListener('click', testVectorAnalysis);
        document.getElementById('compareAPIs').addEventListener('click', compareAPIs);
        
        function updateAPIDisplay() {
            if (useVectorAPI) {
                vectorToggle.classList.remove('regular');
                regularToggle.classList.add('regular');
                currentAPIDisplay.textContent = `Vector API (${vectorAPI})`;
            } else {
                vectorToggle.classList.add('regular');
                regularToggle.classList.remove('regular');
                currentAPIDisplay.textContent = `Regular API (${regularAPI})`;
            }
        }
        
        async function testChatMessage() {
            const message = document.getElementById('testMessage').value;
            const resultDiv = document.getElementById('chatResult');
            const button = document.getElementById('sendMessage');
            
            if (!message.trim()) {
                showResult(resultDiv, 'Please enter a message', 'error');
                return;
            }
            
            button.disabled = true;
            button.textContent = 'Sending...';
            
            try {
                const endpoint = useVectorAPI ? vectorAPI : regularAPI;
                const response = await fetch(endpoint, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        language: 'en'
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    const result = {
                        endpoint: endpoint,
                        response: data.response || data.message,
                        vector_intent: data.vector_intent,
                        vector_confidence: data.vector_confidence,
                        processing_method: data.processing_method,
                        user_intent: data.user_intent,
                        token_usage: data.token_usage
                    };
                    showResult(resultDiv, JSON.stringify(result, null, 2), 'success');
                } else {
                    showResult(resultDiv, `Error: ${data.error}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `Network Error: ${error.message}`, 'error');
            } finally {
                button.disabled = false;
                button.textContent = 'Send Message';
            }
        }
        
        async function testVectorAnalysis() {
            const message = document.getElementById('analysisMessage').value;
            const resultDiv = document.getElementById('analysisResult');
            const button = document.getElementById('analyzeMessage');
            
            if (!message.trim()) {
                showResult(resultDiv, 'Please enter a message', 'error');
                return;
            }
            
            button.disabled = true;
            button.textContent = 'Analyzing...';
            
            try {
                const response = await fetch(analysisAPI, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message
                    })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showResult(resultDiv, JSON.stringify(data.analysis, null, 2), 'success');
                } else {
                    showResult(resultDiv, `Error: ${data.error}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `Network Error: ${error.message}`, 'error');
            } finally {
                button.disabled = false;
                button.textContent = 'Analyze Intent';
            }
        }
        
        async function compareAPIs() {
            const message = document.getElementById('compareMessage').value;
            const resultDiv = document.getElementById('comparisonResult');
            const button = document.getElementById('compareAPIs');
            
            if (!message.trim()) {
                showResult(resultDiv, 'Please enter a message', 'error');
                return;
            }
            
            button.disabled = true;
            button.textContent = 'Comparing...';
            
            try {
                // Test both APIs
                const [vectorResponse, regularResponse] = await Promise.all([
                    fetch(vectorAPI, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ message: message, language: 'en' })
                    }),
                    fetch(regularAPI, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ message: message, language: 'en' })
                    })
                ]);
                
                const vectorData = await vectorResponse.json();
                const regularData = await regularResponse.json();
                
                const comparison = {
                    message: message,
                    vector_api: {
                        status: vectorResponse.status,
                        vector_intent: vectorData.vector_intent,
                        confidence: vectorData.vector_confidence,
                        processing_method: vectorData.processing_method,
                        response_length: vectorData.response?.length || 0
                    },
                    regular_api: {
                        status: regularResponse.status,
                        user_intent: regularData.user_intent,
                        response_length: regularData.response?.length || 0
                    }
                };
                
                showResult(resultDiv, JSON.stringify(comparison, null, 2), 'success');
            } catch (error) {
                showResult(resultDiv, `Comparison Error: ${error.message}`, 'error');
            } finally {
                button.disabled = false;
                button.textContent = 'Compare Both APIs';
            }
        }
        
        function showResult(element, content, type) {
            element.textContent = content;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }
    </script>
</body>
</html>
