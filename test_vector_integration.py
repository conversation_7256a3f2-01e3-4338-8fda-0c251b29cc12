#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script for vector intent integration with response generator
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from new_response_generator import NewResponseGenerator
from gemini_ai import GeminiAI
import j<PERSON>

def test_vector_intent_integration():
    """Test the vector intent detection integration"""
    
    print("🧪 Testing Vector Intent Integration")
    print("=" * 50)
    
    try:
        # Initialize components
        print("📝 Initializing AI components...")
        gemini_ai = GeminiAI()
        response_gen = NewResponseGenerator(gemini_ai)
        
        print("✅ Components initialized successfully!")
        
        # Test cases with expected intents
        test_cases = [
            {
                "input": "Hello, how are you?",
                "description": "Simple greeting",
                "expected_category": "greeting"
            },
            {
                "input": "Tell me about FOIS GROUP company",
                "description": "Company information request",
                "expected_category": "company_info"
            },
            {
                "input": "What job opportunities do you have?",
                "description": "Job search inquiry",
                "expected_category": "job_search"
            },
            {
                "input": "I want to share my CV with you",
                "description": "CV sharing",
                "expected_category": "cv_related"
            },
            {
                "input": "What is the expected salary for Python developers?",
                "description": "Salary inquiry",
                "expected_category": "salary_related"
            },
            {
                "input": "How is the IT market in Vietnam?",
                "description": "Market trends inquiry",
                "expected_category": "market_trends"
            },
            {
                "input": "I'm looking for career opportunities in software development",
                "description": "Formal job search",
                "expected_category": "job_search"
            },
            {
                "input": "Could you provide information about your business services?",
                "description": "Formal company inquiry",
                "expected_category": "company_info"
            }
        ]
        
        print(f"\n🎯 Testing Vector Intent Detection:")
        print("-" * 35)
        
        for i, test_case in enumerate(test_cases, 1):
            user_input = test_case["input"]
            description = test_case["description"]
            expected = test_case["expected_category"]
            
            print(f"\n{i}. {description}")
            print(f"   Input: '{user_input}'")
            print(f"   Expected Category: {expected}")
            
            # Test vector intent detection
            detected_intent, confidence, similarities = response_gen.detect_user_intent_vector(user_input)
            
            print(f"   Vector Result: {detected_intent} (confidence: {confidence:.4f})")
            
            # Test thinking message generation
            print("   🤔 Testing thinking message generation...")
            thinking_response = response_gen.generate_thinking_message(
                user_id="test_user",
                user_input=user_input,
                conversation_history=[]
            )
            
            thinking_msg = thinking_response.get('thinking_message', 'No thinking message')
            ai_detected_intent = thinking_response.get('user_intent', 'unknown')
            
            print(f"   Thinking Message: '{thinking_msg}'")
            print(f"   AI Detected Intent: {ai_detected_intent}")
            
            # Check if vector detection influenced the result
            if detected_intent in ai_detected_intent or ai_detected_intent in detected_intent:
                print("   ✅ Vector detection influenced AI intent")
            else:
                print("   ⚠️ Vector detection may not have influenced AI intent")
        
        print("\n" + "=" * 50)
        print("🎉 Vector intent integration test completed!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_similarity_examples():
    """Test specific similarity examples as requested"""
    print("\n🔍 Testing Similarity Examples")
    print("=" * 35)
    
    try:
        # Initialize components
        gemini_ai = GeminiAI()
        response_gen = NewResponseGenerator(gemini_ai)
        
        # Test the specific example: "Hello friend"
        test_input = "Hello friend"
        print(f"📝 Testing: '{test_input}'")
        
        detected_intent, confidence, similarities = response_gen.detect_user_intent_vector(test_input)
        
        print(f"\nVector Intent Detection Results:")
        print(f"  Best Match: {detected_intent} (confidence: {confidence:.4f})")
        
        # Show all similarities (like the example in the request)
        if hasattr(response_gen.vector_intent_detector, 'vector_detector'):
            vector_similarities = response_gen.vector_intent_detector.vector_detector.detect_intent_vector(test_input)
            
            print(f"\nAll Intent Similarities:")
            sorted_similarities = sorted(vector_similarities.items(), key=lambda x: x[1], reverse=True)
            
            for intent, score in sorted_similarities:
                # Map to response generator intents
                mapped_intent = response_gen.vector_intent_detector.intent_mapping.get(intent, intent)
                print(f"  {intent} → {mapped_intent}: {score:.4f}")
        
        # Test other examples
        other_examples = [
            "Goodbye, see you later",
            "Tell me about your company",
            "I need a job"
        ]
        
        for example in other_examples:
            print(f"\n📝 Testing: '{example}'")
            detected_intent, confidence, _ = response_gen.detect_user_intent_vector(example)
            print(f"  Result: {detected_intent} (confidence: {confidence:.4f})")
        
        print("\n✅ Similarity examples test completed!")
        
    except Exception as e:
        print(f"❌ Error in similarity examples: {e}")
        import traceback
        traceback.print_exc()

def test_cosine_similarity_function():
    """Test the cosine similarity function directly"""
    print("\n🧮 Testing Cosine Similarity Function")
    print("=" * 40)
    
    try:
        from vector_intent_detector import VectorIntentDetector
        
        detector = VectorIntentDetector()
        
        # Test similarity between similar phrases
        text1 = "Hello friend"
        text2 = "Hi there buddy"
        text3 = "Tell me about the company"
        
        vec1 = detector.get_embedding(text1)
        vec2 = detector.get_embedding(text2)
        vec3 = detector.get_embedding(text3)
        
        similarity_12 = detector.cosine_similarity_score(vec1, vec2)
        similarity_13 = detector.cosine_similarity_score(vec1, vec3)
        similarity_23 = detector.cosine_similarity_score(vec2, vec3)
        
        print(f"Text 1: '{text1}'")
        print(f"Text 2: '{text2}'")
        print(f"Text 3: '{text3}'")
        print(f"\nSimilarity Scores:")
        print(f"  Text1 ↔ Text2 (both greetings): {similarity_12:.4f}")
        print(f"  Text1 ↔ Text3 (different topics): {similarity_13:.4f}")
        print(f"  Text2 ↔ Text3 (different topics): {similarity_23:.4f}")
        
        print(f"\n📊 Analysis:")
        if similarity_12 > similarity_13:
            print("  ✅ Similar texts (greetings) have higher similarity")
        else:
            print("  ⚠️ Similar texts don't have higher similarity")
        
        print("✅ Cosine similarity function test completed!")
        
    except Exception as e:
        print(f"❌ Error in cosine similarity test: {e}")

if __name__ == "__main__":
    print("🚀 Vector Intent Integration - Comprehensive Test")
    print("=" * 60)
    
    # Test vector intent integration
    success = test_vector_intent_integration()
    
    # Test similarity examples
    test_similarity_examples()
    
    # Test cosine similarity function
    test_cosine_similarity_function()
    
    if success:
        print("\n💡 Integration Summary:")
        print("=" * 25)
        print("✅ Vector-based intent detection integrated with response generator")
        print("✅ Thinking message generation uses vector intent detection")
        print("✅ Fallback to keyword-based detection for edge cases")
        print("✅ Detailed logging and debugging information")
        print("✅ Cosine similarity calculation working correctly")
        
        print("\n🎯 Key Features:")
        print("- Step 1: ✅ Intent descriptions created")
        print("- Step 2: ✅ Vector calculation using Gemini embeddings")
        print("- Step 3: ✅ Cosine similarity test function implemented")
        print("- Integration: ✅ Vector intent detection in response generator")
        
        print("\n🔧 Usage in Production:")
        print("- Vector intent detection runs automatically")
        print("- Confidence thresholds prevent false positives")
        print("- Fallback ensures system always works")
        print("- Detailed logging helps with debugging")
    else:
        print("\n❌ Integration test failed. Check configuration.")
