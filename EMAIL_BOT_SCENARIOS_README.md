# 📧 Email Bot Test Scenarios for FOIS ICT PRO

## 📋 Overview
This document provides comprehensive test scenarios for the **Email Bot** system supporting 15 specific intents. The email bot is designed to handle professional email communications with comprehensive, complete responses rather than conversational interactions.

---

## 🎯 Supported Intents (15 Total)

### **Core Communication**
- `farewell` - Professional email closings
- `thank_you` - Gratitude expressions in emails
- `off_topic` - Out-of-scope email inquiries

### **Information Requests**
- `ask_company_info` - Company information inquiries
- `ask_bot_info` - Email bot capabilities
- `job_it_trending` - IT market trends and technologies

### **Job-Related Services**
- `search_jobs` - Job search requests
- `filter_jobs` - Specific job filtering
- `apply_job` - Job application submissions

### **Application Management**
- `cancel_application` - Application cancellations
- `upload_resume` - CV submission via email
- `update_resume` - CV update requests

### **Support Services**
- `complaint` - Customer complaints
- `bug_report` - Technical issue reports
- `request_human_support` - Escalation requests

---

## 📧 Email Bot Characteristics

### **Key Differences from Web Chatbot:**
- ✅ **Complete, comprehensive responses** (not conversational)
- ✅ **HTML email format** support
- ✅ **Professional email tone** (more formal than web chat)
- ✅ **Full information delivery** (avoid follow-up questions)
- ✅ **CV file analysis** capability
- ✅ **Real-time email processing**

### **Response Style:**
- **Email Mode:** Comprehensive, complete information
- **Web Mode:** Conversational, interactive
- **Tone:** Professional, polite, detailed
- **Format:** HTML-formatted emails

---

## 🧪 Email Bot Test Scenarios

### **Farewell Intent**
```
📧 Email Subject: "Thank you for your assistance"
📝 Email Content:
- "Thank you for your help. Goodbye."
- "I'm done with my inquiries. Best regards."
- "Cảm ơn sự hỗ trợ. Tạm biệt."

✅ Expected Email Response:
- Professional farewell acknowledgment
- Thank user for contacting FOIS ICT PRO
- Invite future communications
- Include contact information for follow-up
- Professional email signature
- HTML formatted response
```

### **Company Information Intent**
```
📧 Email Subject: "Request for company information"
📝 Email Content:
- "Please provide information about FOIS ICT PRO"
- "I would like to know more about your company"
- "Xin thông tin về công ty FOIS ICT PRO"

✅ Expected Email Response:
- Comprehensive company overview
- Services and expertise areas
- Company mission and values
- Office locations and contact details
- Team size and company culture
- Recent achievements and projects
- Complete information in single email (no follow-ups)
```

### **Job Search Intent**
```
📧 Email Subject: "Job opportunities inquiry"
📝 Email Content:
- "What job positions are currently available?"
- "I'm looking for employment opportunities"
- "Tôi muốn tìm hiểu về cơ hội việc làm"

✅ Expected Email Response:
- Complete list of ALL available positions:
  • Senior Python Developer: 25-35 triệu VNĐ - Hà Nội
  • React Frontend Developer: 20-30 triệu VNĐ - TP.HCM  
  • Senior DevOps Engineer: 40-60 triệu VNĐ - Remote
- Detailed requirements for each position
- Application process and timeline
- Contact information for applications
- NO fake positions or salaries
- Comprehensive single-email response
```

### **CV Upload Intent**
```
📧 Email Subject: "CV Submission"
📝 Email Content:
- "Please find my CV attached for review"
- "I'm submitting my resume for consideration"
- "Đính kèm CV để xem xét"
- [CV file attached: resume.pdf]

✅ Expected Email Response:
- Acknowledge CV receipt
- Analyze attached CV content
- Provide specific feedback based on CV
- Suggest relevant positions from available jobs
- Improvement recommendations
- Next steps for application process
- Professional assessment in HTML format
```

### **Job Application Intent**
```
📧 Email Subject: "Application for Python Developer Position"
📝 Email Content:
- "I want to apply for the Senior Python Developer position"
- "Please process my application for the DevOps role"
- "Tôi muốn ứng tuyển vị trí Python Developer"

✅ Expected Email Response:
- Confirm application receipt
- Detailed application process steps
- Required documents checklist
- Interview timeline and process
- Contact person for follow-up
- Application reference number
- Complete process overview (no follow-up emails needed)
```

### **IT Trending Intent**
```
📧 Email Subject: "IT Market Trends Inquiry"
📝 Email Content:
- "What are the current IT market trends?"
- "Which technologies are in high demand?"
- "Xu hướng công nghệ IT hiện tại như thế nào?"

✅ Expected Email Response:
- Comprehensive IT market analysis
- Hot technologies and skills in demand
- Salary trends for different tech roles
- Market outlook for 2024-2025
- FOIS ICT PRO's technology adoption
- Career advice for IT professionals
- Complete market report in single email
```

### **Filter Jobs Intent**
```
📧 Email Subject: "Remote Python Developer Positions"
📝 Email Content:
- "Do you have any remote Python developer positions?"
- "Show me only senior-level positions"
- "Có vị trí Python remote không?"

✅ Expected Email Response:
- Filtered job results based on criteria
- Only matching real positions from available jobs
- Detailed information for each matching role
- Alternative suggestions if limited results
- Complete filtered list with full details
- NO fake filtered positions
```

### **Complaint Intent**
```
📧 Email Subject: "Service Complaint"
📝 Email Content:
- "I have a complaint about your service"
- "I'm not satisfied with the response I received"
- "Tôi có khiếu nại về dịch vụ"

✅ Expected Email Response:
- Professional acknowledgment of concern
- Sincere apology for any inconvenience
- Request for specific details about the issue
- Explanation of resolution process
- Timeline for investigation and response
- Escalation contact information
- Commitment to service improvement
```

### **Bug Report Intent**
```
📧 Email Subject: "Technical Issue Report"
📝 Email Content:
- "I found a bug on your website"
- "The application form is not working properly"
- "Trang web có lỗi kỹ thuật"

✅ Expected Email Response:
- Acknowledge technical issue report
- Thank user for bringing it to attentiontại
- Request detailed information about the problem
- Provide temporary workarounds if available
- Timeline for technical resolution
- Technical support contact information
- Follow-up process for resolution updates
```

### **Human Support Request Intent**
```
📧 Email Subject: "Request for Human Support"
📝 Email Content:
- "I need to speak with a human representative"
- "Please connect me with your support team"
- "Tôi muốn nói chuyện với nhân viên hỗ trợ"

✅ Expected Email Response:
- Acknowledge request for human assistance
- Provide direct contact information
- Available support hours and response times
- Alternative contact methods (phone, in-person)
- Escalation process explanation
- Immediate assistance options if urgent
- Professional handoff to human team
```

---

## 🔄 Email Conversation Flows

### **Complete Job Application Flow**
```
Email 1: Job Search Inquiry
→ Response: Complete job list with details

Email 2: CV Submission with attachment
→ Response: CV analysis and position recommendations

Email 3: Formal application for specific position
→ Response: Application confirmation and process details

Email 4: Application status inquiry
→ Response: Status update and next steps

Email 5: Thank you message
→ Response: Professional acknowledgment and future contact info
```

### **Technical Support Escalation Flow**
```
Email 1: Bug report
→ Response: Issue acknowledgment and troubleshooting

Email 2: Request for human support
→ Response: Human contact information and escalation

Email 3: Complaint about resolution time
→ Response: Apology and expedited resolution process
```

### **Company Research Flow**
```
Email 1: Company information request
→ Response: Comprehensive company overview

Email 2: IT trends inquiry
→ Response: Market analysis and technology trends

Email 3: Job opportunities based on trends
→ Response: Relevant positions matching market trends
```

---

## ✅ Email Bot Quality Standards

### **Response Requirements:**
- ✅ **Complete information** in single email
- ✅ **HTML formatted** professional emails
- ✅ **No follow-up questions** (provide full details)
- ✅ **Real data only** from sample_data.py
- ✅ **Professional email tone** (more formal than web chat)
- ✅ **Actionable next steps** included
- ✅ **Contact information** provided
- ✅ **CV file analysis** when attachments present

### **Prohibited Behaviors:**
- ❌ **Conversational responses** (use comprehensive format)
- ❌ **Asking follow-up questions** (provide complete info)
- ❌ **Fake job positions** or salary ranges
- ❌ **Incomplete responses** ending abruptly
- ❌ **Plain text emails** (use HTML format)
- ❌ **Generic responses** ignoring email context

---

## 🧪 Testing Protocol

### **Email Testing Steps:**
1. **Send test email** with specific intent
2. **Verify HTML format** in response
3. **Check completeness** of information
4. **Validate real data** usage only
5. **Confirm professional tone**
6. **Test CV attachment** processing
7. **Verify no follow-up** questions asked

### **Success Criteria:**
- ✅ Response within 30 seconds
- ✅ HTML formatted email
- ✅ Complete information provided
- ✅ Professional tone maintained
- ✅ Real data only used
- ✅ CV analysis when applicable
- ✅ Clear next steps provided

---

## 📊 Email vs Web Bot Comparison

| Feature | Email Bot | Web Chatbot |
|---------|-----------|-------------|
| **Response Style** | Comprehensive, complete | Conversational, interactive |
| **Format** | HTML emails | JSON/text responses |
| **Tone** | Formal, professional | Friendly, casual |
| **Information Delivery** | Full details in one response | Step-by-step interaction |
| **Follow-ups** | Avoid asking questions | Encourage conversation |
| **CV Processing** | Analyze attachments | Upload interface |
| **Use Case** | Formal inquiries | Real-time chat |

---

## 🚀 Implementation Notes

### **Email Bot Configuration:**
- **Mode:** `email_mode = True`
- **Response Format:** HTML emails
- **Tone:** Professional and comprehensive
- **Data Source:** Real data from sample_data.py only
- **CV Analysis:** Process email attachments
- **Language:** Auto-detect from email content

### **Key Capabilities:**
- 📧 **Real-time email processing**
- 📎 **CV file analysis**
- 🌍 **Multilingual support** (Vietnamese/English)
- 📊 **Complete job information** delivery
- 🔧 **Professional support** handling
- 📈 **Market analysis** and trends
