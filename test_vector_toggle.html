<!DOCTYPE html>
<html lang="en" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vector Toggle Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .header-actions {
            display: flex;
            gap: 12px;
            align-items: center;
            justify-content: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        
        .btn-icon {
            background: none;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 8px 12px;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-icon:hover {
            background: #e9ecef;
        }
        
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            background: #f0f0f0;
        }
        
        /* Dark theme */
        [data-theme="dark"] {
            background: #1a1a1a;
            color: #e0e0e0;
        }
        
        [data-theme="dark"] .test-container {
            background: #2d2d2d;
            color: #e0e0e0;
        }
        
        [data-theme="dark"] .header-actions {
            background: #3d3d3d;
        }
        
        [data-theme="dark"] .btn-icon {
            border-color: #555;
            color: #e0e0e0;
        }
        
        [data-theme="dark"] .btn-icon:hover {
            background: #4d4d4d;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎯 Vector Toggle Test</h1>
        <p>This page tests the new modern vector toggle component.</p>
        
        <div class="header-actions">
            <!-- Vector toggle will be inserted here -->
            <button class="btn-icon" onclick="toggleTheme()">
                <i class="fas fa-moon"></i> Theme
            </button>
            <button class="btn-icon">
                <i class="fas fa-trash"></i> Clear
            </button>
            <button class="btn-icon">
                <i class="fas fa-info-circle"></i> Info
            </button>
        </div>
        
        <div class="test-info">
            <h3>🧪 Test Instructions:</h3>
            <ol>
                <li>The Vector toggle should appear as the first element in the header actions</li>
                <li>It should look like a modern iOS-style toggle switch</li>
                <li>Click it to toggle between Vector AI and Regular Chat modes</li>
                <li>The toggle should show smooth animations</li>
                <li>A notification should appear when switching modes</li>
                <li>The state should persist in localStorage</li>
            </ol>
        </div>
        
        <div class="status" id="status">
            <strong>Current Mode:</strong> <span id="currentMode">Loading...</span><br>
            <strong>LocalStorage:</strong> <span id="localStorage">Loading...</span>
        </div>
        
        <div style="margin-top: 20px;">
            <button onclick="testToggle()" style="padding: 10px 20px; background: #007AFF; color: white; border: none; border-radius: 6px; cursor: pointer;">
                🔄 Test Toggle Programmatically
            </button>
            <button onclick="clearStorage()" style="padding: 10px 20px; background: #ff3b30; color: white; border: none; border-radius: 6px; cursor: pointer; margin-left: 10px;">
                🗑️ Clear Storage
            </button>
        </div>
    </div>

    <script>
        // Simulate the ChatBot class functionality
        class TestChatBot {
            constructor() {
                this.useVectorAPI = localStorage.getItem('useVectorAPI') === 'true' || false;
                this.vectorAPIEndpoint = '/api/chat-vector';
                this.regularAPIEndpoint = '/api/chat';
                
                this.init();
            }
            
            init() {
                this.setupVectorAPIToggle();
                this.updateStatus();
            }
            
            getCurrentAPIEndpoint() {
                return this.useVectorAPI ? this.vectorAPIEndpoint : this.regularAPIEndpoint;
            }
            
            toggleVectorAPI() {
                this.useVectorAPI = !this.useVectorAPI;
                localStorage.setItem('useVectorAPI', this.useVectorAPI.toString());
                
                const mode = this.useVectorAPI ? 'Vector AI' : 'Regular Chat';
                const emoji = this.useVectorAPI ? '🎯' : '💬';
                
                console.log(`🔄 Switched to ${mode}`);
                this.showModeChangeNotification(mode, emoji);
                this.updateVectorAPIIndicator();
                this.updateStatus();
            }
            
            // Include the same methods from modern.js
            setupVectorAPIToggle() {
                let vectorToggle = document.getElementById('vectorAPIToggle');
                if (!vectorToggle) {
                    this.createVectorAPIToggle();
                } else {
                    vectorToggle.addEventListener('click', () => this.toggleVectorAPI());
                }
                this.updateVectorAPIIndicator();
            }
            
            createVectorAPIToggle() {
                const headerActions = document.querySelector('.header-actions');
                if (headerActions) {
                    const toggleContainer = document.createElement('div');
                    toggleContainer.className = 'vector-api-toggle-container';
                    toggleContainer.innerHTML = `
                        <div class="modern-toggle" id="vectorAPIToggle" title="Toggle between Vector AI and Regular chat">
                            <span class="toggle-label">Vector</span>
                            <div class="toggle-switch">
                                <div class="toggle-slider">
                                    <div class="toggle-circle"></div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Add modern styling (same as in modern.js)
                    const style = document.createElement('style');
                    style.textContent = `
                        .vector-api-toggle-container {
                            display: flex;
                            align-items: center;
                            margin-right: 12px;
                        }
                        
                        .modern-toggle {
                            display: flex;
                            align-items: center;
                            gap: 8px;
                            cursor: pointer;
                            user-select: none;
                            transition: all 0.3s ease;
                        }
                        
                        .toggle-label {
                            font-size: 14px;
                            font-weight: 500;
                            color: var(--text-color, #333);
                            transition: color 0.3s ease;
                        }
                        
                        .toggle-switch {
                            position: relative;
                            width: 50px;
                            height: 28px;
                            background: #e0e0e0;
                            border-radius: 20px;
                            transition: all 0.3s ease;
                            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
                        }
                        
                        .toggle-slider {
                            position: absolute;
                            top: 2px;
                            left: 2px;
                            width: 24px;
                            height: 24px;
                            background: white;
                            border-radius: 50%;
                            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
                            box-shadow: 0 2px 6px rgba(0,0,0,0.2);
                        }
                        
                        .toggle-circle {
                            width: 100%;
                            height: 100%;
                            border-radius: 50%;
                            background: white;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            font-size: 10px;
                            transition: all 0.3s ease;
                        }
                        
                        .modern-toggle.active .toggle-switch {
                            background: linear-gradient(135deg, #007AFF, #0056CC);
                            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1), 0 0 0 2px rgba(0, 122, 255, 0.2);
                        }
                        
                        .modern-toggle.active .toggle-slider {
                            transform: translateX(22px);
                            background: white;
                        }
                        
                        .modern-toggle.active .toggle-label {
                            color: #007AFF;
                            font-weight: 600;
                        }
                        
                        .modern-toggle.active .toggle-circle::before {
                            content: "🎯";
                            font-size: 10px;
                        }
                        
                        .modern-toggle:not(.active) .toggle-circle::before {
                            content: "💬";
                            font-size: 10px;
                        }
                        
                        .modern-toggle:hover .toggle-switch {
                            transform: scale(1.05);
                        }
                        
                        .modern-toggle:hover .toggle-label {
                            color: #007AFF;
                        }
                        
                        [data-theme="dark"] .toggle-label {
                            color: #e0e0e0;
                        }
                        
                        [data-theme="dark"] .toggle-switch {
                            background: #404040;
                        }
                        
                        [data-theme="dark"] .modern-toggle:hover .toggle-label {
                            color: #4A9EFF;
                        }
                        
                        @keyframes toggleBounce {
                            0% { transform: scale(1); }
                            50% { transform: scale(1.1); }
                            100% { transform: scale(1); }
                        }
                        
                        .modern-toggle.changing {
                            animation: toggleBounce 0.3s ease;
                        }
                    `;
                    document.head.appendChild(style);

                    headerActions.insertBefore(toggleContainer, headerActions.firstChild);

                    const toggleButton = document.getElementById('vectorAPIToggle');
                    toggleButton.addEventListener('click', () => this.toggleVectorAPI());
                }
            }
            
            updateVectorAPIIndicator() {
                const toggleButton = document.getElementById('vectorAPIToggle');
                const toggleLabel = toggleButton?.querySelector('.toggle-label');

                if (toggleButton) {
                    toggleButton.classList.add('changing');
                    
                    if (this.useVectorAPI) {
                        toggleButton.classList.add('active');
                        if (toggleLabel) {
                            toggleLabel.textContent = 'Vector';
                        }
                    } else {
                        toggleButton.classList.remove('active');
                        if (toggleLabel) {
                            toggleLabel.textContent = 'Regular';
                        }
                    }

                    setTimeout(() => {
                        toggleButton.classList.remove('changing');
                    }, 300);
                }
            }
            
            showModeChangeNotification(mode, emoji) {
                const existingNotification = document.querySelector('.mode-notification');
                if (existingNotification) {
                    existingNotification.remove();
                }

                const notification = document.createElement('div');
                notification.className = 'mode-notification';
                notification.innerHTML = `
                    <span class="mode-emoji">${emoji}</span>
                    <span class="mode-text">Switched to ${mode}</span>
                `;

                const style = document.createElement('style');
                style.textContent = `
                    .mode-notification {
                        position: fixed;
                        top: 20px;
                        right: 20px;
                        background: rgba(0, 122, 255, 0.95);
                        color: white;
                        padding: 12px 20px;
                        border-radius: 25px;
                        display: flex;
                        align-items: center;
                        gap: 8px;
                        font-size: 14px;
                        font-weight: 500;
                        box-shadow: 0 4px 20px rgba(0, 122, 255, 0.3);
                        z-index: 10000;
                        animation: slideInNotification 0.3s ease-out;
                        backdrop-filter: blur(10px);
                    }

                    @keyframes slideInNotification {
                        from { transform: translateX(100%); opacity: 0; }
                        to { transform: translateX(0); opacity: 1; }
                    }

                    @keyframes slideOutNotification {
                        from { transform: translateX(0); opacity: 1; }
                        to { transform: translateX(100%); opacity: 0; }
                    }

                    .mode-notification.hiding {
                        animation: slideOutNotification 0.3s ease-in;
                    }
                `;
                
                if (!document.querySelector('#mode-notification-styles')) {
                    style.id = 'mode-notification-styles';
                    document.head.appendChild(style);
                }

                document.body.appendChild(notification);

                setTimeout(() => {
                    notification.classList.add('hiding');
                    setTimeout(() => {
                        if (notification.parentNode) {
                            notification.remove();
                        }
                    }, 300);
                }, 2000);
            }
            
            updateStatus() {
                document.getElementById('currentMode').textContent = this.useVectorAPI ? 'Vector AI 🎯' : 'Regular Chat 💬';
                document.getElementById('localStorage').textContent = localStorage.getItem('useVectorAPI') || 'null';
            }
        }
        
        // Helper functions
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
            const newTheme = currentTheme === 'light' ? 'dark' : 'light';
            document.documentElement.setAttribute('data-theme', newTheme);
        }
        
        function testToggle() {
            if (window.testBot) {
                window.testBot.toggleVectorAPI();
            }
        }
        
        function clearStorage() {
            localStorage.removeItem('useVectorAPI');
            if (window.testBot) {
                window.testBot.useVectorAPI = false;
                window.testBot.updateVectorAPIIndicator();
                window.testBot.updateStatus();
            }
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', () => {
            window.testBot = new TestChatBot();
        });
    </script>
</body>
</html>
