# 🚀 FOIS ICT PRO Chatbot - Build, Run & Deploy Guide

## 📋 Table of Contents
- [Prerequisites](#prerequisites)
- [Environment Setup](#environment-setup)
- [Build Instructions](#build-instructions)
- [Run Instructions](#run-instructions)
- [Deploy Instructions](#deploy-instructions)
- [Troubleshooting](#troubleshooting)

---



## 🌍 Environment Setup

### 1. Clone Repository
```bash
git clone <repository-url>
cd chatbox-ai
```

### 2. Create Virtual Environment
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# On Windows:
venv\Scripts\activate
# On macOS/Linux:
source venv/bin/activate
```

### 3. Install Dependencies
```bash
# Install Python dependencies
pip install -r requirements.txt

```

### 4. Environment Variables
Create `.env` file in project root:
```env
# Required
GEMINI_API_KEY=your_gemini_api_key_here

# Optional - Email Configuration
SMTP_SERVER=smtp.gmail.com
SMTP_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASSWORD=your_app_password

# Optional - Application Settings
FLASK_ENV=development
FLASK_DEBUG=True
PORT=5001
```

---


## 🏃‍♂️ Run Instructions

### Development Mode

#### Option 1: Web Interface (Recommended)
```bash
# Run web application
python run_web.py

# Or alternatively:
python web_app.py
```
- **Access**: http://localhost:5001
- **Features**: Full web interface with modern UI

#### Option 2: Console Mode
```bash
# Run console chatbot
python main.py
```
- **Features**: Command-line interface for testing

#### Option 3: Email Mode
```bash
# Run email chatbot (requires email configuration)
python email_chatbot.py
```
