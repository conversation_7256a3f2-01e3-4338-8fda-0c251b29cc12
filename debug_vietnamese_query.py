#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Debug script for Vietnamese query intent classification issue
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_vietnamese_query():
    """Debug the specific Vietnamese query that's returning 'other'"""
    
    print("🔍 Debugging Vietnamese Query Intent Classification")
    print("=" * 60)
    
    try:
        from vector_intent_detector import VectorIntentDetector
        from vector_chatbot.vector_chatbot_router import VectorChatbotRouter
        
        # Initialize components
        print("🚀 Initializing components...")
        vector_detector = VectorIntentDetector()
        vector_router = VectorChatbotRouter()
        print("✅ Components initialized successfully!\n")
        
        # The problematic Vietnamese query
        problematic_query = "có vị trí nào phù hợp với tôi ở Fois không nhỉ?"
        
        print(f"🎯 Debugging Problematic Query:")
        print(f"📝 Query: '{problematic_query}'")
        print(f"🎯 Expected Intent: 'search_jobs'")
        print("-" * 60)
        
        # Step 1: Test vector intent detection directly
        print("🔍 Step 1: Direct Vector Intent Detection")
        vector_intent, confidence, similarities = vector_detector.get_best_intent(problematic_query)
        print(f"   Result: {vector_intent} (confidence: {confidence:.4f})")
        
        # Show all similarities sorted
        print(f"   All Intent Similarities:")
        sorted_similarities = sorted(similarities.items(), key=lambda x: x[1], reverse=True)
        for i, (intent, sim) in enumerate(sorted_similarities):
            marker = "👑" if i == 0 else "🔸" if intent == "search_jobs" else "  "
            print(f"      {marker} {intent}: {sim:.4f}")
        
        # Check search_jobs similarity specifically
        search_jobs_sim = similarities.get('search_jobs', 0)
        print(f"\n   🎯 search_jobs similarity: {search_jobs_sim:.4f}")
        
        # Step 2: Test vector router processing
        print(f"\n🎨 Step 2: Vector Router Processing")
        router_result = vector_router.process_user_input(problematic_query, "test_user", [])
        
        print(f"   Router Results:")
        print(f"      Vector Intent: {router_result.get('vector_intent', 'unknown')}")
        print(f"      Mapped Intent: {router_result.get('mapped_intent', 'unknown')}")
        print(f"      Confidence: {router_result.get('confidence', 0):.4f}")
        print(f"      Needs Feedback: {router_result.get('needs_feedback', False)}")
        
        # Step 3: Analyze why search_jobs might have low similarity
        print(f"\n🔬 Step 3: Analyzing search_jobs Intent Descriptions")
        
        search_jobs_descriptions = vector_detector.intent_descriptions.get('search_jobs', [])
        print(f"   Number of descriptions: {len(search_jobs_descriptions)}")
        
        # Check if our Vietnamese keywords are in the descriptions
        vietnamese_keywords = ["vị trí", "phù hợp", "công ty", "Fois", "không"]
        
        for i, desc in enumerate(search_jobs_descriptions):
            print(f"   Description {i+1}: {desc[:100]}...")
            
            # Check for Vietnamese keywords
            found_keywords = [kw for kw in vietnamese_keywords if kw in desc]
            if found_keywords:
                print(f"      Found keywords: {found_keywords}")
        
        # Step 4: Test similar Vietnamese queries
        print(f"\n🧪 Step 4: Testing Similar Vietnamese Queries")
        
        similar_queries = [
            "có việc làm nào phù hợp với tôi không?",
            "vị trí nào phù hợp với CV của tôi?",
            "có job nào phù hợp không?",
            "tìm việc phù hợp với tôi",
            "có công việc nào cho tôi ở công ty không?"
        ]
        
        for query in similar_queries:
            intent, conf, _ = vector_detector.get_best_intent(query)
            status = "✅" if intent == "search_jobs" else "❌"
            print(f"   {status} '{query}' → {intent} ({conf:.3f})")
        
        # Step 5: Check threshold issues
        print(f"\n⚖️ Step 5: Threshold Analysis")
        
        threshold_used = 0.25 if vector_intent in ["search_jobs", "filter_jobs", "cv_feedback", "salary_query", "job_it_trending"] else 0.3
        print(f"   Threshold for {vector_intent}: {threshold_used}")
        print(f"   Confidence: {confidence:.4f}")
        print(f"   Above threshold: {'✅ YES' if confidence >= threshold_used else '❌ NO'}")
        
        if confidence < threshold_used:
            print(f"   🔍 Issue: Confidence below threshold!")
            print(f"   💡 Suggestion: Lower threshold or improve intent descriptions")
        
        # Step 6: Recommendations
        print(f"\n💡 Step 6: Recommendations")
        
        if vector_intent == "other":
            print("   🔧 Current Issues:")
            print("   1. Query is being classified as 'other' instead of 'search_jobs'")
            print("   2. Similarity score might be too low")
            print("   3. Vietnamese keywords might not be well represented")
            
            print("\n   🛠️ Suggested Fixes:")
            print("   1. Add more Vietnamese job search phrases to search_jobs descriptions")
            print("   2. Lower the threshold for job-related intents")
            print("   3. Improve Vietnamese language support in embeddings")
            print("   4. Add specific company name patterns (Fois, FOIS ICT PRO)")
        else:
            print("   ✅ Query is being classified correctly!")
        
        return vector_intent == "search_jobs"
        
    except Exception as e:
        print(f"❌ Critical error in Vietnamese query debug: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_vietnamese_job_queries():
    """Test various Vietnamese job search queries"""
    
    print("\n🇻🇳 Testing Vietnamese Job Search Queries")
    print("=" * 60)
    
    try:
        from vector_intent_detector import VectorIntentDetector
        
        vector_detector = VectorIntentDetector()
        
        vietnamese_job_queries = [
            {
                "query": "có vị trí nào phù hợp với tôi ở Fois không nhỉ?",
                "description": "Original problematic query"
            },
            {
                "query": "có việc làm nào phù hợp với tôi không?",
                "description": "General job suitability"
            },
            {
                "query": "vị trí nào phù hợp với CV của tôi?",
                "description": "CV-based position search"
            },
            {
                "query": "có job nào phù hợp không?",
                "description": "Mixed Vietnamese-English"
            },
            {
                "query": "tìm việc phù hợp với kỹ năng của tôi",
                "description": "Skills-based job search"
            },
            {
                "query": "có công việc nào cho tôi ở công ty không?",
                "description": "Company-specific job search"
            }
        ]
        
        correct_count = 0
        total_count = len(vietnamese_job_queries)
        
        for i, test_case in enumerate(vietnamese_job_queries, 1):
            query = test_case["query"]
            description = test_case["description"]
            
            print(f"\n📝 Test {i}: {description}")
            print(f"   Query: '{query}'")
            
            intent, confidence, similarities = vector_detector.get_best_intent(query)
            
            if intent == "search_jobs":
                print(f"   ✅ CORRECT: {intent} (confidence: {confidence:.3f})")
                correct_count += 1
            else:
                print(f"   ❌ INCORRECT: {intent} (confidence: {confidence:.3f})")
                
                # Show search_jobs similarity
                search_jobs_sim = similarities.get('search_jobs', 0)
                print(f"   📊 search_jobs similarity: {search_jobs_sim:.3f}")
                
                # Show top 3 for comparison
                top_3 = sorted(similarities.items(), key=lambda x: x[1], reverse=True)[:3]
                print(f"   🔝 Top 3:")
                for intent_name, sim in top_3:
                    print(f"      - {intent_name}: {sim:.3f}")
        
        accuracy = (correct_count / total_count) * 100
        print(f"\n📊 Vietnamese Query Results:")
        print(f"   Correct: {correct_count}/{total_count}")
        print(f"   Accuracy: {accuracy:.1f}%")
        
        return accuracy >= 70
        
    except Exception as e:
        print(f"❌ Error in Vietnamese query test: {e}")
        return False


def main():
    """Run the Vietnamese query debug tests"""
    print(f"🕒 Debug started at: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Debug the specific problematic query
    specific_test_passed = debug_vietnamese_query()
    
    # Test various Vietnamese job queries
    vietnamese_test_passed = test_vietnamese_job_queries()
    
    print()
    print(f"🕒 Debug completed at: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("\n" + "=" * 60)
    print("🎯 SUMMARY:")
    print(f"   Specific Query Test: {'✅ PASSED' if specific_test_passed else '❌ FAILED'}")
    print(f"   Vietnamese Queries Test: {'✅ PASSED' if vietnamese_test_passed else '❌ FAILED'}")
    
    if specific_test_passed and vietnamese_test_passed:
        print("\n🎉 Vietnamese query classification is working correctly!")
    else:
        print("\n🔧 Issues found with Vietnamese query classification:")
        print("1. Vietnamese job search phrases need better representation")
        print("2. Threshold might be too high for Vietnamese queries")
        print("3. Company-specific terms (Fois) need to be included")
        print("4. Mixed Vietnamese-English queries need support")
    
    return specific_test_passed and vietnamese_test_passed


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
