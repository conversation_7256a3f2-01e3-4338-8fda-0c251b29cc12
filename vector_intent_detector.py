#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vector-based Intent Detection using Gemini Embeddings
"""

import numpy as np
from sklearn.metrics.pairwise import cosine_similarity
import google.generativeai as genai
from typing import Dict, List, Tuple
import json
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class VectorIntentDetector:
    """Vector-based intent detection using embeddings"""

    # Class-level cache for intent vectors to avoid recomputation
    _intent_vectors_cache = None
    _api_configured = False
    _initialization_count = 0

    def __init__(self):
        """Initialize the vector intent detector"""
        # Configure Gemini API only once
        if not VectorIntentDetector._api_configured:
            api_key = os.getenv("GEMINI_API_KEY")
            if not api_key:
                raise ValueError(
                    "GEMINI_API_KEY not found in environment variables")

            genai.configure(api_key=api_key)
            VectorIntentDetector._api_configured = True

        # Define intent descriptions for vector calculation
        self.intent_descriptions = {
            "greeting": [
                "Hello, hi, good morning, good afternoon, good evening, greetings, hey there",
                "<PERSON><PERSON><PERSON> bạn, xin chào, ch<PERSON>o buổi sáng, chào buổi chiều, chào buổi tối",
                "Friendly greeting and welcoming messages"
            ],
            "farewell": [
                "Goodbye, bye, see you later, farewell, take care, until next time",
                "Tạm biệt, chào tạm bi<PERSON>t, hẹn gặp lại, chúc bạn tốt",
                "Saying goodbye and ending conversations"
            ],
            "ask_company_info": [
                "Tell me about the company, company information, what does your company do",
                "Thông tin về công ty, công ty làm gì, giới thiệu về công ty FOIS",
                "Questions about company background, services, history, and general information"
            ],
            "ask_bot_info": [
                "How to use this platform, how does this system work, user guide, what can you do",
                "Hướng dẫn sử dụng, cách dùng hệ thống, hướng dẫn platform, bot làm gì được",
                "Questions about how to use the chatbot platform and its features"
            ],
            "search_jobs": [
                "Job openings, available positions, career opportunities, hiring, find jobs for me",
                "Cơ hội việc làm, vị trí tuyển dụng, tuyển dụng, việc làm, tìm việc cho tôi",
                "Questions about job opportunities, open positions, and career prospects",
                "Find jobs that match my CV, show me suitable positions, jobs for my skills, recommend jobs",
                "Tìm việc phù hợp với CV, vị trí phù hợp với kỹ năng, công việc cho tôi, gợi ý việc làm",
                "CV-based job search requests and job matching queries based on background and experience",
                "Jobs matching my profile, positions for my background, suitable roles for my qualifications",
                "Việc làm phù hợp với hồ sơ, vị trí cho background của tôi, vai trò phù hợp với trình độ",
                "What jobs are available for someone with my experience, find me jobs that fit my profile",
                "Có vị trí nào phù hợp với tôi không, vị trí nào phù hợp với tôi, có việc làm nào cho tôi",
                "Có job nào phù hợp không, có công việc nào phù hợp, vị trí phù hợp ở công ty",
                "Are there any positions suitable for me, any suitable positions, jobs for me at company",
                "Có vị trí nào phù hợp với tôi ở Fois không, có việc làm nào ở công ty phù hợp với tôi",
                "Có vị trí nào phù hợp với tôi ở Fois không nhỉ, có job nào ở Fois phù hợp với tôi",
                "Fois có vị trí nào cho tôi không, FOIS ICT PRO có việc làm nào phù hợp",
                "Công ty có vị trí nào phù hợp với background của tôi không",
                "Tìm việc ở Fois, việc làm tại FOIS ICT PRO, cơ hội nghề nghiệp ở công ty"
            ],
            "filter_jobs": [
                "Job requirements, job description, specific job details, filter by criteria, this job",
                "Yêu cầu công việc, mô tả công việc cụ thể, lọc theo tiêu chí, công việc này",
                "Specific questions about job requirements, filtering, and detailed job information",
                "What are the requirements for this job, tell me about this position, job details",
                "Yêu cầu của công việc này là gì, nói về vị trí này, chi tiết công việc",
                "Questions about specific job qualifications, responsibilities, and detailed information"
            ],
            "cv_feedback": [
                "My CV, my resume, my experience, my skills, my background, review my CV",
                "CV của tôi, kinh nghiệm của tôi, kỹ năng của tôi, hồ sơ cá nhân, đánh giá CV",
                "Sharing personal profile, CV, resume, or requesting CV feedback and review"
            ],
            "salary_query": [
                "Salary expectation, expected salary, compensation, pay, wage, salary range",
                "Mong muốn lương, lương mong đợi, mức lương, thu nhập, khoảng lương",
                "Questions about salary expectations and compensation discussions"
            ],
            "apply_job": [
                "I want to apply for this job, submit application, apply for position, send my CV",
                "Tôi muốn ứng tuyển, nộp đơn ứng tuyển, apply vào vị trí này, gửi CV",
                "Requests to apply for specific job positions and submit applications"
            ],
            "cancel_application": [
                "Cancel my application, withdraw application, remove my application, don't want to apply",
                "Hủy đơn ứng tuyển, rút đơn, không muốn apply nữa, hủy application",
                "Requests to cancel or withdraw job applications"
            ],
            "upload_resume": [
                "Upload my CV, attach resume, send my CV file, here is my resume",
                "Upload CV của tôi, đính kèm CV, gửi file CV, đây là CV của tôi",
                "Actions related to uploading CV/resume files to the system"
            ],
            "update_resume": [
                "Update my CV, modify resume, change my profile, edit my information",
                "Cập nhật CV, sửa đổi hồ sơ, thay đổi thông tin cá nhân, chỉnh sửa CV",
                "Requests to update or modify existing CV/resume information"
            ],
            "get_application_status": [
                "Check application status, my application progress, status of my job application",
                "Kiểm tra trạng thái ứng tuyển, tiến trình đơn ứng tuyển, tình trạng application",
                "Inquiries about the status and progress of submitted job applications"
            ],
            "job_recommendation": [
                "Recommend jobs for me, suggest suitable positions, what jobs fit my profile",
                "Gợi ý việc làm cho tôi, đề xuất vị trí phù hợp, công việc nào phù hợp với tôi",
                "Requests for job recommendations and suggestions based on profile"
            ],
            "follow_up_job": [
                "Follow up on my application, any updates on my job application, when will I hear back",
                "Theo dõi đơn ứng tuyển, có cập nhật gì về application không, khi nào có phản hồi",
                "Following up on submitted applications and checking for updates"
            ],
            "location_query": [
                "Office location, where is the company located, work address, company address",
                "Địa chỉ công ty, văn phòng ở đâu, vị trí làm việc, địa điểm công ty",
                "Questions about office locations, work addresses, and company locations"
            ],
            "tech_stack_query": [
                "What technologies do you use, tech stack, programming languages, development tools",
                "Công nghệ gì được sử dụng, tech stack, ngôn ngữ lập trình, công cụ phát triển",
                "Questions about technology stack, programming languages, and development tools"
            ],
            "interview_process": [
                "Interview process, how is the interview, interview steps, what to expect in interview",
                "Quy trình phỏng vấn, phỏng vấn như thế nào, các bước phỏng vấn, chuẩn bị gì cho interview",
                "Questions about interview procedures, process, and what to expect"
            ],
            "complaint": [
                "I have a complaint, problem with service, not satisfied, issue with system",
                "Tôi có khiếu nại, vấn đề với dịch vụ, không hài lòng, có vấn đề với hệ thống",
                "Complaints about service quality, system issues, or dissatisfaction"
            ],
            "bug_report": [
                "System bug, error in application, technical problem, something is not working",
                "Lỗi hệ thống, lỗi trong ứng dụng, vấn đề kỹ thuật, có gì đó không hoạt động",
                "Reports of technical bugs, system errors, and functionality issues"
            ],
            "request_human_support": [
                "Talk to human, speak with staff, need human help, contact support team",
                "Nói chuyện với người thật, liên hệ nhân viên, cần hỗ trợ từ con người, team support",
                "Requests to speak with human staff or get assistance from support team"
            ],
            "job_it_trending": [
                "IT trends, trending technologies, hot skills, technology trends, what's popular",
                "Xu hướng IT, công nghệ hot, kỹ năng trending, xu hướng công nghệ",
                "Questions about trending IT technologies and popular skills in the market"
            ],
            "thank_you": [
                "Thank you, thanks, appreciate it, grateful, much appreciated",
                "Cảm ơn, cám ơn bạn, biết ơn, cảm kích",
                "Expressing gratitude and appreciation"
            ],
            "smalltalk": [
                "How are you, nice weather, casual conversation, small talk",
                "Bạn khỏe không, thời tiết đẹp, trò chuyện phiếm",
                "Casual conversation and small talk not related to work"
            ],
            "off_topic": [
                "Off-topic, unrelated, random questions, general chat, not work related",
                "Ngoài chủ đề, không liên quan, câu hỏi ngẫu nhiên, trò chuyện chung",
                "Questions not related to jobs, company, or recruitment"
            ],
            "other": [
                "General questions, miscellaneous, other topics, unclear intent",
                "Câu hỏi chung, chủ đề khác, ý định không rõ ràng",
                "Other questions that don't fit into specific categories"
            ]
        }

        # Use cached intent vectors if available
        VectorIntentDetector._initialization_count += 1
        if VectorIntentDetector._intent_vectors_cache is None:
            print(
                f"🔄 Initializing intent vectors... (attempt #{VectorIntentDetector._initialization_count})")
            self.intent_vectors = {}
            self._initialize_intent_vectors()
            VectorIntentDetector._intent_vectors_cache = self.intent_vectors
            print("🎉 Intent vectors initialized successfully!")
        else:
            print(
                f"📦 Using cached intent vectors... (attempt #{VectorIntentDetector._initialization_count})")
            self.intent_vectors = VectorIntentDetector._intent_vectors_cache

    def get_embedding(self, text: str) -> np.ndarray:
        """Get embedding vector for text using Gemini"""
        try:
            response = genai.embed_content(
                model="models/text-embedding-004",
                content=text,
                task_type="RETRIEVAL_DOCUMENT"
            )
            return np.array(response['embedding'])
        except Exception as e:
            print(f"Error getting embedding: {e}")
            # Return zero vector as fallback
            return np.zeros(768)  # Default embedding size

    def cosine_similarity_score(self, vector1: np.ndarray, vector2: np.ndarray) -> float:
        """Calculate cosine similarity between two vectors"""
        try:
            # Ensure vectors are 2D for sklearn
            vec1 = vector1.reshape(1, -1)
            vec2 = vector2.reshape(1, -1)
            return cosine_similarity(vec1, vec2)[0][0]
        except Exception as e:
            print(f"Error calculating cosine similarity: {e}")
            return 0.0

    def _initialize_intent_vectors(self):
        """Initialize vectors for all intent descriptions"""
        print("🔄 Initializing intent vectors...")

        for intent, descriptions in self.intent_descriptions.items():
            # Combine all descriptions for this intent
            combined_description = " ".join(descriptions)

            # Get embedding vector
            vector = self.get_embedding(combined_description)
            self.intent_vectors[intent] = vector

            print(f"✅ {intent}: vector shape {vector.shape}")

        print("🎉 Intent vectors initialized successfully!")

    def detect_intent_vector(self, user_input: str) -> Dict[str, float]:
        """Detect intent using vector similarity"""
        # Get embedding for user input
        user_vector = self.get_embedding(user_input)

        # Calculate similarity with each intent
        similarities = {}

        for intent, intent_vector in self.intent_vectors.items():
            similarity = self.cosine_similarity_score(
                user_vector, intent_vector)
            similarities[intent] = similarity

        return similarities

    def get_best_intent(self, user_input: str, threshold: float = 0.3) -> Tuple[str, float, Dict[str, float]]:
        """Get the best matching intent with confidence score"""
        similarities = self.detect_intent_vector(user_input)

        # Find the best match
        best_intent = max(similarities, key=similarities.get)
        best_score = similarities[best_intent]

        # If best score is below threshold, classify as other
        if best_score < threshold:
            best_intent = "other"
            best_score = similarities.get("other", 0.0)

        return best_intent, best_score, similarities


def test_similarity():
    """Unit test function for similarity calculation"""
    print("🧪 Testing Similarity Function")
    print("=" * 40)

    # Initialize detector
    detector = VectorIntentDetector()

    # Test cases with updated intent names
    test_cases = [
        {
            "input": "Hello friend",
            "expected_top": "greeting",
            "description": "Simple greeting"
        },
        {
            "input": "Tell me about FOIS ICT PRO company",
            "expected_top": "ask_company_info",
            "description": "Company information request"
        },
        {
            "input": "What job opportunities do you have?",
            "expected_top": "search_jobs",
            "description": "Job opportunities inquiry"
        },
        {
            "input": "Can you find jobs that match my CV skills?",
            "expected_top": "search_jobs",
            "description": "CV-based job search - should be search_jobs not filter_jobs"
        },
        {
            "input": "Show me positions suitable for my background",
            "expected_top": "search_jobs",
            "description": "Background-based job search"
        },
        {
            "input": "Here is my CV and experience",
            "expected_top": "cv_feedback",
            "description": "Profile sharing and CV feedback"
        },
        {
            "input": "What is my expected salary?",
            "expected_top": "salary_query",
            "description": "Salary discussion"
        },
        {
            "input": "What are the trending IT technologies?",
            "expected_top": "job_it_trending",
            "description": "IT trends inquiry"
        },
        {
            "input": "Goodbye, see you later",
            "expected_top": "farewell",
            "description": "Farewell message"
        }
    ]

    print("🎯 Running Test Cases:")
    print("-" * 25)

    for i, test_case in enumerate(test_cases, 1):
        user_input = test_case["input"]
        expected = test_case["expected_top"]
        description = test_case["description"]

        print(f"\n{i}. {description}")
        print(f"   Input: '{user_input}'")

        # Get intent detection results
        best_intent, best_score, all_similarities = detector.get_best_intent(
            user_input)

        print(f"   Best Intent: {best_intent} (score: {best_score:.4f})")
        print(f"   Expected: {expected}")

        # Show top 3 similarities
        sorted_similarities = sorted(
            all_similarities.items(), key=lambda x: x[1], reverse=True)
        print("   Top 3 similarities:")
        for intent, score in sorted_similarities[:3]:
            print(f"     - {intent}: {score:.4f}")

        # Check if prediction matches expectation
        if best_intent == expected:
            print("   ✅ PASS")
        else:
            print("   ⚠️ DIFFERENT (but may still be valid)")

    print("\n" + "=" * 40)
    print("🎉 Similarity test completed!")

    return detector


if __name__ == "__main__":
    print("🚀 Vector-based Intent Detection System")
    print("=" * 50)

    # Run the similarity test
    detector = test_similarity()

    print("\n💡 Usage Example:")
    print("-" * 15)

    # Interactive example
    example_input = "Hello, how are you today?"
    best_intent, confidence, all_scores = detector.get_best_intent(
        example_input)

    print(f"Input: '{example_input}'")
    print(f"Detected Intent: {best_intent}")
    print(f"Confidence: {confidence:.4f}")
    print("\nAll Scores:")
    for intent, score in sorted(all_scores.items(), key=lambda x: x[1], reverse=True):
        print(f"  {intent}: {score:.4f}")
