#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify intent classification consistency between vector and regular chat
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_intent_classification():
    """Test intent classification for CV-based job search queries"""
    
    print("🧪 Testing Intent Classification Consistency")
    print("=" * 60)
    
    try:
        from vector_intent_detector import VectorIntentDetector
        from vector_chatbot.vector_chatbot_router import VectorChatbotRouter
        
        # Initialize components
        print("🚀 Initializing components...")
        vector_detector = VectorIntentDetector()
        vector_router = VectorChatbotRouter()
        print("✅ Components initialized successfully!\n")
        
        # Test cases that should be classified as 'search_jobs'
        cv_job_search_queries = [
            "Can you find jobs that match my CV skills?",
            "Show me positions suitable for my background",
            "What jobs are available for someone with my experience?",
            "Find me jobs that fit my profile",
            "I want to see job opportunities that match my CV",
            "Can you recommend jobs based on my skills?",
            "Show me suitable positions for my qualifications",
            "What positions would be good for my background?",
            "Find jobs matching my expertise",
            "I'm looking for jobs that suit my CV"
        ]
        
        # Test cases that should be classified as 'filter_jobs' (job details)
        job_detail_queries = [
            "What are the requirements for this specific job?",
            "Tell me more details about the Python developer position",
            "What is the job description for the frontend role?",
            "What are the specific qualifications needed?",
            "Can you explain the responsibilities of this job?",
            "What skills are required for this position?",
            "Tell me about the benefits of this job",
            "What is the salary range for this specific role?"
        ]
        
        print("🎯 Testing CV-based Job Search Queries (should be 'search_jobs'):")
        print("-" * 70)
        
        search_jobs_correct = 0
        search_jobs_total = len(cv_job_search_queries)
        
        for i, query in enumerate(cv_job_search_queries, 1):
            print(f"\n📝 Query {i}: {query}")
            
            # Test vector intent detection
            vector_intent, confidence, similarities = vector_detector.get_best_intent(query)
            
            # Test vector router processing
            router_result = vector_router.process_user_input(query, "test_user", [])
            mapped_intent = router_result.get('mapped_intent', 'unknown')
            
            print(f"   🔍 Vector Intent: {vector_intent} (confidence: {confidence:.3f})")
            print(f"   🎯 Mapped Intent: {mapped_intent}")
            
            # Check if correctly classified
            if mapped_intent == 'search_jobs':
                print(f"   ✅ CORRECT: Classified as 'search_jobs'")
                search_jobs_correct += 1
            else:
                print(f"   ❌ INCORRECT: Expected 'search_jobs', got '{mapped_intent}'")
                
                # Show top similarities for debugging
                top_3 = sorted(similarities.items(), key=lambda x: x[1], reverse=True)[:3]
                print(f"   🔍 Top 3 similarities:")
                for intent, sim in top_3:
                    print(f"      - {intent}: {sim:.3f}")
        
        print(f"\n📊 CV Job Search Results: {search_jobs_correct}/{search_jobs_total} correct")
        
        print("\n" + "🎯 Testing Job Detail Queries (should be 'filter_jobs'):")
        print("-" * 70)
        
        filter_jobs_correct = 0
        filter_jobs_total = len(job_detail_queries)
        
        for i, query in enumerate(job_detail_queries, 1):
            print(f"\n📝 Query {i}: {query}")
            
            # Test vector intent detection
            vector_intent, confidence, similarities = vector_detector.get_best_intent(query)
            
            # Test vector router processing
            router_result = vector_router.process_user_input(query, "test_user", [])
            mapped_intent = router_result.get('mapped_intent', 'unknown')
            
            print(f"   🔍 Vector Intent: {vector_intent} (confidence: {confidence:.3f})")
            print(f"   🎯 Mapped Intent: {mapped_intent}")
            
            # Check if correctly classified
            if mapped_intent == 'filter_jobs':
                print(f"   ✅ CORRECT: Classified as 'filter_jobs'")
                filter_jobs_correct += 1
            else:
                print(f"   ❌ INCORRECT: Expected 'filter_jobs', got '{mapped_intent}'")
        
        print(f"\n📊 Job Detail Results: {filter_jobs_correct}/{filter_jobs_total} correct")
        
        # Overall results
        total_correct = search_jobs_correct + filter_jobs_correct
        total_queries = search_jobs_total + filter_jobs_total
        accuracy = (total_correct / total_queries) * 100
        
        print("\n" + "=" * 60)
        print("🎉 OVERALL RESULTS:")
        print(f"✅ CV Job Search Accuracy: {search_jobs_correct}/{search_jobs_total} ({(search_jobs_correct/search_jobs_total)*100:.1f}%)")
        print(f"✅ Job Detail Accuracy: {filter_jobs_correct}/{filter_jobs_total} ({(filter_jobs_correct/filter_jobs_total)*100:.1f}%)")
        print(f"🎯 Total Accuracy: {total_correct}/{total_queries} ({accuracy:.1f}%)")
        
        if accuracy >= 80:
            print("🎉 EXCELLENT: Intent classification is working well!")
        elif accuracy >= 60:
            print("⚠️ GOOD: Intent classification needs some improvement")
        else:
            print("❌ POOR: Intent classification needs significant improvement")
        
        return accuracy >= 80
        
    except Exception as e:
        print(f"❌ Critical error in intent classification test: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run the intent classification test"""
    print(f"🕒 Test started at: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    success = test_intent_classification()
    
    print()
    print(f"🕒 Test completed at: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success:
        print("✅ Intent classification test passed!")
        print("\n💡 Key Findings:")
        print("✅ CV-based job search queries are correctly classified as 'search_jobs'")
        print("✅ Job detail queries are correctly classified as 'filter_jobs'")
        print("✅ Vector and regular chat modes should have consistent intent classification")
    else:
        print("❌ Intent classification test failed!")
        print("\n🔧 Recommendations:")
        print("1. Review vector intent descriptions for 'search_jobs' vs 'filter_jobs'")
        print("2. Add more CV-specific keywords to 'search_jobs' intent")
        print("3. Improve intent similarity calculations")
        print("4. Check confidence thresholds for job-related intents")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
