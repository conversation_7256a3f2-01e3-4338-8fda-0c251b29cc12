#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify vector chat has access to all the same data as regular chat
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_data_imports():
    """Test that both systems can import the same data"""
    print("🧪 Testing Data Import Completeness")
    print("=" * 50)
    
    # Test regular chat imports
    try:
        from sample_data import (
            SAMPLE_JOBS, SALARY_RANGES, JOB_MARKET_INSIGHTS_2024,
            HOT_TECH_STACK_2024, IT_MARKET_TRENDS_2024_2025, 
            FREELANCE_MARKET_2024, IT_FORECAST_2025
        )
        print("✅ Regular chat data imports: SUCCESS")
        print(f"   - SAMPLE_JOBS: {len(SAMPLE_JOBS)} jobs")
        print(f"   - SALARY_RANGES: {len(SALARY_RANGES)} salary levels")
        print(f"   - HOT_TECH_STACK_2024: {len(HOT_TECH_STACK_2024)} tech categories")
        print(f"   - IT_MARKET_TRENDS_2024_2025: Available")
        print(f"   - FREELANCE_MARKET_2024: Available")
        print(f"   - IT_FORECAST_2025: Available")
        
    except Exception as e:
        print(f"❌ Regular chat data imports: FAILED - {e}")
        return False
    
    # Test vector chat imports
    try:
        from vector_chatbot.vector_chatbot_router import VectorChatbotRouter
        print("✅ Vector chat imports: SUCCESS")
        
    except Exception as e:
        print(f"❌ Vector chat imports: FAILED - {e}")
        return False
    
    return True


def test_blockchain_data_availability():
    """Test that blockchain data is available in both systems"""
    print("\n🔗 Testing Blockchain Data Availability")
    print("=" * 50)
    
    from sample_data import HOT_TECH_STACK_2024, SALARY_RANGES, IT_FORECAST_2025
    
    # Check blockchain in HOT_TECH_STACK_2024
    if 'blockchain' in HOT_TECH_STACK_2024:
        blockchain_tech = HOT_TECH_STACK_2024['blockchain']
        print("✅ Blockchain in HOT_TECH_STACK_2024:")
        print(f"   - Salary Range: {blockchain_tech['salary_range']}")
        print(f"   - Demand: {blockchain_tech['demand']}")
        print(f"   - Note: {blockchain_tech['note']}")
        print(f"   - Trending: {blockchain_tech['trending']}")
    else:
        print("❌ Blockchain NOT found in HOT_TECH_STACK_2024")
        return False
    
    # Check blockchain engineer in SALARY_RANGES
    if 'blockchain_engineer' in SALARY_RANGES:
        blockchain_salary = SALARY_RANGES['blockchain_engineer']
        print("✅ Blockchain Engineer in SALARY_RANGES:")
        print(f"   - Range: {blockchain_salary['range']}")
        print(f"   - Description: {blockchain_salary['description']}")
        print(f"   - Market Trend: {blockchain_salary['market_trend']}")
        print(f"   - Skills: {blockchain_salary['skills']}")
    else:
        print("❌ Blockchain Engineer NOT found in SALARY_RANGES")
        return False
    
    # Check blockchain in IT_FORECAST_2025
    if 'blockchain_dev' in IT_FORECAST_2025['salary_predictions']:
        blockchain_forecast = IT_FORECAST_2025['salary_predictions']['blockchain_dev']
        print("✅ Blockchain Dev in IT_FORECAST_2025:")
        print(f"   - 2025 Prediction: {blockchain_forecast}")
    else:
        print("❌ Blockchain Dev NOT found in IT_FORECAST_2025")
        return False
    
    return True


def test_comprehensive_market_context():
    """Test that vector chat can build comprehensive market context"""
    print("\n📊 Testing Comprehensive Market Context")
    print("=" * 50)
    
    try:
        from vector_chatbot.vector_chatbot_router import VectorChatbotRouter
        
        # Initialize router
        router = VectorChatbotRouter()
        
        # Test comprehensive market context method
        context = router._build_comprehensive_market_context()
        
        print("✅ Comprehensive market context generated")
        print(f"   - Context length: {len(context)} characters")
        
        # Check for key blockchain mentions
        blockchain_mentions = context.lower().count('blockchain')
        print(f"   - Blockchain mentions: {blockchain_mentions}")
        
        if blockchain_mentions >= 3:  # Should appear in multiple sections
            print("✅ Blockchain data properly included")
        else:
            print("❌ Blockchain data missing or insufficient")
            return False
            
        # Check for key sections
        required_sections = [
            "HOT TECHNOLOGIES & SALARY RANGES",
            "TOP PAYING POSITIONS", 
            "MARKET TRENDS",
            "FREELANCE MARKET",
            "MOST DEMANDED POSITIONS",
            "2025 FORECAST",
            "HOT JOB OPPORTUNITIES",
            "SALARY PREDICTIONS"
        ]
        
        missing_sections = []
        for section in required_sections:
            if section not in context:
                missing_sections.append(section)
        
        if missing_sections:
            print(f"❌ Missing sections: {missing_sections}")
            return False
        else:
            print("✅ All required sections present")
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing comprehensive context: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_completeness_comparison():
    """Compare data completeness between regular and vector chat"""
    print("\n⚖️ Testing Data Completeness Comparison")
    print("=" * 50)
    
    from sample_data import SAMPLE_JOBS
    from vector_chatbot.vector_chatbot_router import VectorChatbotRouter
    
    # Test regular chat job access
    regular_jobs_count = len(SAMPLE_JOBS)
    print(f"📊 Regular chat has access to: {regular_jobs_count} jobs")
    
    # Test vector chat job access
    try:
        router = VectorChatbotRouter()
        
        # Simulate job feedback response to see how many jobs it uses
        test_response = router._generate_job_feedback_response(
            "Tell me about blockchain developer jobs", 
            "test_user", 
            [], 
            "search_jobs"
        )
        
        # Check if response contains blockchain information
        response_message = test_response.get('message', '').lower()
        blockchain_in_response = 'blockchain' in response_message
        
        print(f"✅ Vector chat job feedback generated")
        print(f"   - Contains blockchain info: {blockchain_in_response}")
        print(f"   - Response length: {len(response_message)} characters")
        
        if blockchain_in_response:
            print("✅ Vector chat has access to blockchain data")
        else:
            print("❌ Vector chat missing blockchain data")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing vector chat: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all data completeness tests"""
    print("🚀 Vector Chat Data Completeness Test Suite")
    print("=" * 60)
    print()
    
    tests = [
        test_data_imports,
        test_blockchain_data_availability,
        test_comprehensive_market_context,
        test_data_completeness_comparison
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ PASSED\n")
            else:
                print("❌ FAILED\n")
        except Exception as e:
            print(f"❌ FAILED with exception: {e}\n")
    
    print("=" * 60)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Vector chat has complete data access.")
        print("\n💡 Key Improvements Made:")
        print("✅ Vector chat now uses ALL SAMPLE_JOBS (not just top 3)")
        print("✅ Vector chat includes comprehensive market context")
        print("✅ Vector chat has access to blockchain data")
        print("✅ Vector chat includes HOT_TECH_STACK_2024")
        print("✅ Vector chat includes IT_MARKET_TRENDS_2024_2025")
        print("✅ Vector chat includes FREELANCE_MARKET_2024")
        print("✅ Vector chat includes IT_FORECAST_2025")
        print("✅ Vector chat matches regular chat data completeness")
    else:
        print("⚠️ Some tests failed. Vector chat may be missing data.")
        
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
