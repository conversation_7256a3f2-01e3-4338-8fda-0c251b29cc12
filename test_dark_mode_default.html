<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Theme Toggle Test</title>
    <link rel="stylesheet" href="static/css/modern.css">
</head>
<body>
    <div style="padding: 20px;">
        <h1>Theme Toggle Test</h1>
        <p>This page should load with light mode by default. Click the theme toggle button to switch!</p>

        <!-- Comprehensive Text Test Components -->
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">

            <!-- Sidebar Header Test -->
            <div class="sidebar-header" style="padding: 15px; border-radius: 8px;">
                <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 15px;">
                    <div class="logo-icon">
                        <i class="fas fa-briefcase"></i>
                    </div>
                    <div class="logo-text">
                        <h2>FOIS ICT PRO</h2>
                        <span>Logo with gradient background</span>
                    </div>
                </div>
                <div class="company-header">
                    <h3>Company Header</h3>
                </div>
                <div class="detail-item">
                    <i class="fas fa-building"></i>
                    <span>Detail item text</span>
                </div>
                <p style="margin-top: 10px; font-size: 12px;">
                    <strong>Logo Icon Test:</strong><br>
                    • Light mode: Light gradient background<br>
                    • Dark mode: Dark gradient background<br>
                    • Text: Always white on gradient
                </p>
            </div>

            <!-- Chat Header Test -->
            <div class="chat-header" style="padding: 15px; border-radius: 8px;">
                <div class="header-info">
                    <div class="info">
                        <h3>Chat Header Test</h3>
                        <div class="status">Status text should be WHITE</div>
                        <div class="status online">Online status</div>
                    </div>
                </div>
                <button class="mobile-menu-btn">
                    <i class="fas fa-bars"></i>
                </button>
            </div>

            <!-- Messages Container Test -->
            <div class="messages-container" style="padding: 15px; border-radius: 8px; min-height: 150px;">
                <div class="bot-message">
                    <div class="message-bubble" style="margin: 10px 0;">
                        <p><strong>🤖 Bot Message with Gradient Background:</strong></p>
                        <p>• Light mode: DARK text on LIGHT gradient background</p>
                        <p>• Dark mode: WHITE text on DARK gradient background</p>
                        <p>This bubble should have a gradient background that changes with theme!</p>
                    </div>
                </div>
                <div class="user-message">
                    <div class="message-bubble">
                        <p><strong>👤 User Message with Primary Gradient:</strong></p>
                        <p>• Both modes: WHITE text on primary gradient</p>
                        <p>• Light mode: Blue gradient</p>
                        <p>• Dark mode: Purple gradient</p>
                    </div>
                </div>
                <div class="welcome-message">
                    <div class="message-bubble">
                        <p><strong>🎉 Welcome Message with Welcome Gradient:</strong></p>
                        <p>• Light mode: Dark text on light welcome gradient</p>
                        <p>• Dark mode: White text on dark welcome gradient</p>
                    </div>
                </div>
            </div>

            <!-- Suggestions Container Test -->
            <div class="suggestions-container" style="padding: 15px; border-radius: 8px;">
                <div class="suggestions-header">
                    <i class="fas fa-lightbulb"></i>
                    <span>Suggestions Header - WHITE text</span>
                </div>
                <div class="suggestion-item" style="margin: 10px 0;">
                    Suggestion item - WHITE text
                </div>
                <div class="feature-item" style="margin: 10px 0;">
                    Feature item - WHITE text
                </div>
            </div>

            <!-- Input Container Test -->
            <div class="input-container" style="padding: 15px; border-radius: 8px;">
                <div class="input-field">
                    <input type="text" id="messageInput" placeholder="Input placeholder - should be visible" style="width: 100%; border: none; background: transparent;">
                </div>
                <div class="powered-by">Powered by text</div>
            </div>

            <!-- Action Items Test -->
            <div style="padding: 15px; border-radius: 8px; background: var(--bg-secondary);">
                <div class="quick-actions">
                    <h4>Quick Actions Header</h4>
                </div>
                <div class="action-item" style="margin: 10px 0;">
                    <i class="fas fa-star"></i>
                    Action item text - WHITE
                </div>
                <button class="btn-secondary">Secondary Button</button>
                <button class="btn-icon">
                    <i class="fas fa-cog"></i>
                </button>
            </div>

        </div>

        <!-- Additional Text Elements Test -->
        <div style="padding: 20px; background: var(--bg-secondary); border-radius: 8px; margin: 20px 0;">
            <h1>H1 Heading - Should be WHITE</h1>
            <h2>H2 Heading - Should be WHITE</h2>
            <h3>H3 Heading - Should be WHITE</h3>
            <p>Paragraph text - Should be WHITE in dark mode</p>
            <span>Span text - Should be WHITE</span>
            <div>Div text - Should be WHITE</div>

            <div class="lang-current" style="margin: 10px; display: inline-block;">
                Language selector - WHITE text
            </div>

            <div class="token-usage-header" style="margin: 10px 0;">
                <span>Token Usage Header</span>
            </div>

            <div class="token-label">Token Label</div>
            <div class="token-detail-value">Token Detail Value</div>
        </div>

        <!-- Company Card Test -->
        <div class="company-card" style="margin: 20px 0;">
            <div class="company-header">
                <i class="fas fa-building"></i>
                <h3>Company Card Test</h3>
            </div>
            <div class="company-details">
                <div class="detail-item">
                    <i class="fas fa-map-marker-alt"></i>
                    <span>Company detail item 1 - Should be WHITE in dark mode</span>
                </div>
                <div class="detail-item">
                    <i class="fas fa-phone"></i>
                    <span>Company detail item 2 - Should be WHITE in dark mode</span>
                </div>
                <div class="detail-item">
                    <i class="fas fa-envelope"></i>
                    <span>Company detail item 3 - Should be WHITE in dark mode</span>
                </div>
            </div>
            <p style="margin-top: 15px;">
                This entire company card should have:
                <br>• <strong>Light mode:</strong> White background with dark text
                <br>• <strong>Dark mode:</strong> Dark background with WHITE text
            </p>
        </div>

        <button class="btn-icon" id="themeToggle" style="margin: 10px;" title="Toggle Theme">
            <i class="fas fa-moon"></i>
        </button>

        <p><strong>Click the button above to toggle between light and dark themes!</strong></p>
        
        <div style="background: var(--bg-primary); color: var(--text-primary); padding: 20px; margin: 20px 0; border-radius: 8px; border: 1px solid var(--border-color);">
            <h3>Theme Variables Test</h3>
            <p>Background: var(--bg-primary)</p>
            <p>Text: var(--text-primary)</p>
            <p>Border: var(--border-color)</p>
            <p id="themeStatus">Theme will be detected automatically...</p>
        </div>
    </div>
    
    <script src="static/js/modern.js"></script>
    <script>
        // Initialize theme system
        let chatBot;

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize the chatbot to get theme functionality
            chatBot = new ModernChatBot();

            // Add theme toggle functionality
            const themeToggle = document.getElementById('themeToggle');
            if (themeToggle) {
                themeToggle.addEventListener('click', function() {
                    chatBot.toggleTheme();
                    updateStatus();
                });
            }

            // Initial status update
            updateStatus();
        });

        function updateStatus() {
            const theme = document.documentElement.getAttribute('data-theme') || 'light';
            const computedBg = getComputedStyle(document.documentElement).getPropertyValue('--bg-primary').trim();

            console.log('Current theme:', theme);
            console.log('Background color:', computedBg);

            // Update theme status text
            const themeStatus = document.getElementById('themeStatus');
            if (themeStatus) {
                if (theme === 'dark') {
                    themeStatus.textContent = '🌙 Dark mode is active! Background should be dark with light text.';
                } else {
                    themeStatus.textContent = '☀️ Light mode is active! Background should be light with dark text.';
                }
            }

            // Create or update status display
            let status = document.getElementById('statusDisplay');
            if (!status) {
                status = document.createElement('div');
                status.id = 'statusDisplay';
                status.style.cssText = 'position: fixed; top: 10px; right: 10px; background: var(--bg-secondary); color: var(--text-primary); padding: 15px; border-radius: 8px; font-family: monospace; border: 1px solid var(--border-color); box-shadow: var(--shadow-color) 0 4px 12px;';
                document.body.appendChild(status);
            }

            const isLight = theme === 'light' || (!theme && (computedBg === '#ffffff' || computedBg === 'rgb(255, 255, 255)'));
            const isDark = theme === 'dark' || (computedBg === '#0f222a' || computedBg === 'rgb(15, 34, 42)');

            status.innerHTML = `
                <strong>🎨 Theme Toggle Test</strong><br>
                Current Theme: <strong>${theme || 'default'}</strong><br>
                BG Color: ${computedBg}<br>
                Status: ${isLight ? '☀️ LIGHT MODE' : isDark ? '🌙 DARK MODE' : '❓ UNKNOWN'}<br>
                <small>Click moon/sun button to toggle!</small>
            `;
        }
    </script>
</body>
</html>
