#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify that vector intent detection results are properly used
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from new_response_generator import NewResponseGenerator
from gemini_ai import GeminiAI
import j<PERSON>

def test_vector_intent_usage():
    """Test that vector intent detection results are actually used"""
    
    print("🧪 Testing Vector Intent Usage")
    print("=" * 40)
    
    try:
        # Initialize components
        print("📝 Initializing AI components...")
        gemini_ai = GeminiAI()
        response_gen = NewResponseGenerator(gemini_ai)
        
        print("✅ Components initialized successfully!")
        
        # Test cases to verify vector intent is being used
        test_cases = [
            {
                "input": "Tell me about FOIS GROUP company",
                "description": "Company info request",
                "expected_vector_intent": "ask_company_info"
            },
            {
                "input": "What job opportunities do you have?",
                "description": "Job search request",
                "expected_vector_intent": "search_jobs"
            },
            {
                "input": "I want to share my CV",
                "description": "CV sharing request",
                "expected_vector_intent": "cv_feedback"
            }
        ]
        
        print(f"\n🎯 Testing Vector Intent Usage:")
        print("-" * 30)
        
        for i, test_case in enumerate(test_cases, 1):
            user_input = test_case["input"]
            description = test_case["description"]
            expected = test_case["expected_vector_intent"]
            
            print(f"\n{i}. {description}")
            print(f"   Input: '{user_input}'")
            print(f"   Expected Vector Intent: {expected}")
            
            # Test thinking message generation
            thinking_response = response_gen.generate_thinking_message(
                user_id="test_user",
                user_input=user_input,
                conversation_history=[]
            )
            
            # Check if vector intent analysis is included in response
            vector_analysis = thinking_response.get('vector_intent_analysis', {})
            
            if vector_analysis:
                detected_intent = vector_analysis.get('detected_intent', 'unknown')
                confidence = vector_analysis.get('confidence', 0.0)
                top_similarities = vector_analysis.get('top_similarities', {})
                
                print(f"   ✅ Vector Analysis Found:")
                print(f"     - Detected Intent: {detected_intent}")
                print(f"     - Confidence: {confidence:.4f}")
                print(f"     - Top Similarities: {list(top_similarities.keys())[:3]}")
                
                # Check if AI's detected intent matches or is influenced by vector intent
                ai_intent = thinking_response.get('user_intent', 'unknown')
                thinking_msg = thinking_response.get('thinking_message', 'No message')
                
                print(f"   🤖 AI Response:")
                print(f"     - AI Detected Intent: {ai_intent}")
                print(f"     - Thinking Message: '{thinking_msg}'")
                
                # Check if vector intent influenced the AI decision
                if detected_intent == ai_intent:
                    print("   ✅ PERFECT MATCH: Vector intent exactly matches AI intent")
                elif detected_intent in ai_intent or ai_intent in detected_intent:
                    print("   ✅ GOOD MATCH: Vector intent influenced AI intent")
                elif confidence > 0.7:
                    print("   ⚠️ HIGH CONFIDENCE MISMATCH: Vector had high confidence but AI chose different intent")
                else:
                    print("   ℹ️ DIFFERENT: AI used its own judgment (vector confidence was lower)")
                
            else:
                print("   ❌ No vector analysis found in response")
            
            # Check if needs_callback is set appropriately
            needs_callback = thinking_response.get('needs_callback', False)
            print(f"   📞 Needs Callback: {needs_callback}")
        
        print("\n" + "=" * 40)
        print("🎉 Vector intent usage test completed!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_vector_confidence_influence():
    """Test how vector confidence influences AI decisions"""
    print("\n🎯 Testing Vector Confidence Influence")
    print("=" * 40)
    
    try:
        gemini_ai = GeminiAI()
        response_gen = NewResponseGenerator(gemini_ai)
        
        # Test cases with different expected confidence levels
        confidence_test_cases = [
            {
                "input": "Hello, how are you?",
                "description": "Simple greeting (may have lower confidence)",
                "expected_confidence": "medium"
            },
            {
                "input": "Tell me about FOIS GROUP",
                "description": "Clear company request (should have high confidence)",
                "expected_confidence": "high"
            },
            {
                "input": "What's the weather like?",
                "description": "Off-topic question (should have lower confidence)",
                "expected_confidence": "low"
            }
        ]
        
        for i, test_case in enumerate(confidence_test_cases, 1):
            user_input = test_case["input"]
            description = test_case["description"]
            expected_conf = test_case["expected_confidence"]
            
            print(f"\n{i}. {description}")
            print(f"   Input: '{user_input}'")
            print(f"   Expected Confidence: {expected_conf}")
            
            # Get vector intent detection directly
            detected_intent, confidence, similarities = response_gen.detect_user_intent_vector(user_input)
            
            print(f"   Vector Results:")
            print(f"     - Intent: {detected_intent}")
            print(f"     - Confidence: {confidence:.4f}")
            
            # Categorize confidence
            if confidence > 0.7:
                conf_category = "HIGH"
            elif confidence > 0.4:
                conf_category = "MEDIUM"
            else:
                conf_category = "LOW"
            
            print(f"     - Category: {conf_category}")
            
            # Test thinking message
            thinking_response = response_gen.generate_thinking_message(
                user_id="test_user",
                user_input=user_input,
                conversation_history=[]
            )
            
            ai_intent = thinking_response.get('user_intent', 'unknown')
            print(f"   AI Intent: {ai_intent}")
            
            # Check if high confidence vector intents are respected
            if confidence > 0.7 and detected_intent == ai_intent:
                print("   ✅ HIGH CONFIDENCE: AI respected vector intent")
            elif confidence > 0.7 and detected_intent != ai_intent:
                print("   ⚠️ HIGH CONFIDENCE IGNORED: AI chose different intent despite high vector confidence")
            else:
                print("   ℹ️ NORMAL: AI made independent decision")
        
        print("\n✅ Vector confidence influence test completed!")
        
    except Exception as e:
        print(f"❌ Error in confidence test: {e}")

if __name__ == "__main__":
    print("🚀 Vector Intent Usage Verification")
    print("=" * 50)
    
    # Test that vector intent results are being used
    success = test_vector_intent_usage()
    
    # Test confidence influence
    test_vector_confidence_influence()
    
    if success:
        print("\n💡 Summary:")
        print("=" * 15)
        print("✅ Vector intent detection results are now properly used")
        print("✅ Enhanced prompt includes vector analysis")
        print("✅ Response includes vector analysis for debugging")
        print("✅ AI can consider vector confidence levels")
        print("✅ No more 'unused variable' warnings")
        
        print("\n🎯 Key Improvements:")
        print("- detected_intent: Used in enhanced prompt")
        print("- confidence: Used to guide AI decision-making")
        print("- similarities: Included in response for debugging")
        print("- Vector analysis: Added to response data")
    else:
        print("\n❌ Vector usage test failed. Check configuration.")
