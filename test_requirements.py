#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify requirements.txt has all necessary dependencies
"""

import sys
import importlib

def test_import(module_name, package_name=None, optional=False):
    """Test if a module can be imported"""
    try:
        importlib.import_module(module_name)
        status = "✅ AVAILABLE"
        return True
    except ImportError as e:
        if optional:
            status = "⚠️ OPTIONAL (not installed)"
        else:
            status = f"❌ MISSING: {str(e)}"
        return False
    finally:
        package_display = package_name or module_name
        print(f"   {package_display}: {status}")

def main():
    """Test all dependencies from requirements.txt"""
    print("🧪 Testing Requirements.txt Dependencies")
    print("=" * 50)
    
    # Core dependencies (required)
    print("\n📦 CORE DEPENDENCIES (Required):")
    core_success = 0
    core_total = 0
    
    core_modules = [
        ("flask", "Flask"),
        ("flask_cors", "Flask-CORS"),
        ("werkzeug", "Werkzeug"),
        ("dotenv", "python-dotenv"),
        ("requests", "Requests"),
        ("google.generativeai", "google-generativeai"),
        ("google.genai", "google-genai"),
        ("numpy", "NumPy"),
        ("typing_extensions", "typing-extensions"),
        ("dateutil", "python-dateutil")
    ]
    
    for module, package in core_modules:
        core_total += 1
        if test_import(module, package):
            core_success += 1
    
    # CV Processing dependencies (optional)
    print("\n📄 CV PROCESSING DEPENDENCIES (Optional):")
    cv_success = 0
    cv_total = 0
    
    cv_modules = [
        ("PyPDF2", "PyPDF2"),
        ("docx", "python-docx"),
        ("pdfplumber", "pdfplumber"),
        ("PIL", "Pillow"),
        ("magic", "python-magic")
    ]
    
    for module, package in cv_modules:
        cv_total += 1
        if test_import(module, package, optional=True):
            cv_success += 1
    
    # Production dependencies (optional)
    print("\n🏭 PRODUCTION DEPENDENCIES (Optional):")
    prod_success = 0
    prod_total = 0
    
    prod_modules = [
        ("waitress", "Waitress")
    ]
    
    for module, package in prod_modules:
        prod_total += 1
        if test_import(module, package, optional=True):
            prod_success += 1
    
    # Summary
    print("\n📊 DEPENDENCY TEST SUMMARY:")
    print("=" * 30)
    print(f"Core Dependencies: {core_success}/{core_total} ({'✅ READY' if core_success == core_total else '❌ MISSING REQUIRED'})")
    print(f"CV Processing: {cv_success}/{cv_total} ({'✅ FULL' if cv_success == cv_total else '⚠️ PARTIAL' if cv_success > 0 else '❌ NONE'})")
    print(f"Production: {prod_success}/{prod_total} ({'✅ READY' if prod_success == prod_total else '⚠️ MISSING'})")
    
    # Recommendations
    print("\n💡 RECOMMENDATIONS:")
    if core_success < core_total:
        print("❌ Install core dependencies: pip install -r requirements.txt")
    elif cv_success < cv_total:
        print("⚠️ Some CV features may not work. Full install: pip install -r requirements.txt")
    elif prod_success < prod_total:
        print("⚠️ Production server not available. Install: pip install -r requirements.txt")
    else:
        print("✅ All dependencies available! System ready for full functionality.")
    
    # Feature availability
    print("\n🎯 FEATURE AVAILABILITY:")
    print(f"   Vector Chatbot: {'✅ YES' if core_success >= 8 else '❌ NO (missing core deps)'}")
    print(f"   CV Upload: {'✅ YES' if cv_success >= 3 else '⚠️ LIMITED' if cv_success > 0 else '❌ NO'}")
    print(f"   Production Mode: {'✅ YES' if prod_success > 0 else '❌ NO (will use Flask dev server)'}")
    
    return core_success == core_total

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Requirements test PASSED! System ready to run.")
        sys.exit(0)
    else:
        print("\n❌ Requirements test FAILED! Install missing dependencies.")
        sys.exit(1)
