#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script specifically for CV-based job search intent classification
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_cv_job_search_intent():
    """Test CV-based job search intent classification"""
    
    print("🎯 Testing CV-based Job Search Intent Classification")
    print("=" * 60)
    
    try:
        from vector_intent_detector import VectorIntentDetector
        from vector_chatbot.vector_chatbot_router import VectorChatbotRouter
        
        # Initialize components
        print("🚀 Initializing components...")
        vector_detector = VectorIntentDetector()
        vector_router = VectorChatbotRouter()
        print("✅ Components initialized successfully!\n")
        
        # Test cases that should be classified as 'search_jobs'
        cv_job_search_queries = [
            {
                "query": "Can you find jobs that match my CV skills?",
                "description": "Direct CV skills matching request"
            },
            {
                "query": "Show me positions suitable for my background",
                "description": "Background-based position request"
            },
            {
                "query": "What jobs are available for someone with my experience?",
                "description": "Experience-based job inquiry"
            },
            {
                "query": "Find me jobs that fit my profile",
                "description": "Profile-based job search"
            },
            {
                "query": "I want to see job opportunities that match my CV",
                "description": "CV matching opportunities"
            },
            {
                "query": "Can you recommend jobs based on my skills?",
                "description": "Skills-based job recommendation"
            },
            {
                "query": "Show me suitable positions for my qualifications",
                "description": "Qualifications-based position search"
            }
        ]
        
        print("🎯 Testing CV-based Job Search Queries:")
        print("-" * 60)
        
        correct_classifications = 0
        total_queries = len(cv_job_search_queries)
        
        for i, test_case in enumerate(cv_job_search_queries, 1):
            query = test_case["query"]
            description = test_case["description"]
            
            print(f"\n📝 Test {i}: {description}")
            print(f"   Query: '{query}'")
            
            # Test vector intent detection
            vector_intent, confidence, similarities = vector_detector.get_best_intent(query)
            
            # Test vector router processing
            router_result = vector_router.process_user_input(query, "test_user", [])
            mapped_intent = router_result.get('mapped_intent', 'unknown')
            
            print(f"   🔍 Vector Intent: {vector_intent} (confidence: {confidence:.3f})")
            print(f"   🎯 Mapped Intent: {mapped_intent}")
            
            # Check if correctly classified
            if mapped_intent == 'search_jobs':
                print(f"   ✅ CORRECT: Classified as 'search_jobs'")
                correct_classifications += 1
            else:
                print(f"   ❌ INCORRECT: Expected 'search_jobs', got '{mapped_intent}'")
                
                # Show top similarities for debugging
                top_3 = sorted(similarities.items(), key=lambda x: x[1], reverse=True)[:3]
                print(f"   🔍 Top 3 similarities:")
                for intent, sim in top_3:
                    print(f"      - {intent}: {sim:.3f}")
                
                # Check if search_jobs was close
                search_jobs_sim = similarities.get('search_jobs', 0)
                print(f"   📊 search_jobs similarity: {search_jobs_sim:.3f}")
        
        # Calculate accuracy
        accuracy = (correct_classifications / total_queries) * 100
        
        print(f"\n" + "=" * 60)
        print("📊 RESULTS:")
        print(f"✅ Correct Classifications: {correct_classifications}/{total_queries}")
        print(f"🎯 Accuracy: {accuracy:.1f}%")
        
        if accuracy >= 85:
            print("🎉 EXCELLENT: CV job search intent classification is working well!")
            return True
        elif accuracy >= 70:
            print("⚠️ GOOD: CV job search intent classification needs minor improvement")
            return False
        else:
            print("❌ POOR: CV job search intent classification needs significant improvement")
            return False
        
    except Exception as e:
        print(f"❌ Critical error in CV job search intent test: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_intent_distinction():
    """Test that search_jobs and filter_jobs are properly distinguished"""
    
    print("\n🔍 Testing Intent Distinction (search_jobs vs filter_jobs)")
    print("=" * 60)
    
    try:
        from vector_intent_detector import VectorIntentDetector
        
        vector_detector = VectorIntentDetector()
        
        # Test cases for search_jobs
        search_jobs_queries = [
            "Find jobs for my skills",
            "Show me available positions",
            "What job opportunities do you have?",
            "Jobs matching my CV"
        ]
        
        # Test cases for filter_jobs  
        filter_jobs_queries = [
            "What are the requirements for this job?",
            "Tell me details about this position",
            "Job description for Python developer",
            "What skills are needed for this role?"
        ]
        
        print("🔍 Testing search_jobs queries:")
        search_correct = 0
        for query in search_jobs_queries:
            intent, confidence, _ = vector_detector.get_best_intent(query)
            print(f"   '{query}' → {intent} ({confidence:.3f})")
            if intent == 'search_jobs':
                search_correct += 1
                print(f"      ✅ Correct")
            else:
                print(f"      ❌ Expected search_jobs")
        
        print(f"\n🔍 Testing filter_jobs queries:")
        filter_correct = 0
        for query in filter_jobs_queries:
            intent, confidence, _ = vector_detector.get_best_intent(query)
            print(f"   '{query}' → {intent} ({confidence:.3f})")
            if intent == 'filter_jobs':
                filter_correct += 1
                print(f"      ✅ Correct")
            else:
                print(f"      ❌ Expected filter_jobs")
        
        search_accuracy = (search_correct / len(search_jobs_queries)) * 100
        filter_accuracy = (filter_correct / len(filter_jobs_queries)) * 100
        
        print(f"\n📊 Distinction Results:")
        print(f"   search_jobs accuracy: {search_correct}/{len(search_jobs_queries)} ({search_accuracy:.1f}%)")
        print(f"   filter_jobs accuracy: {filter_correct}/{len(filter_jobs_queries)} ({filter_accuracy:.1f}%)")
        
        return search_accuracy >= 75 and filter_accuracy >= 75
        
    except Exception as e:
        print(f"❌ Error in intent distinction test: {e}")
        return False


def main():
    """Run the CV job search intent tests"""
    print(f"🕒 Test started at: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # Test CV-based job search intent classification
    cv_test_passed = test_cv_job_search_intent()
    
    # Test intent distinction
    distinction_test_passed = test_intent_distinction()
    
    print()
    print(f"🕒 Test completed at: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print("\n" + "=" * 60)
    print("🎯 FINAL SUMMARY:")
    print(f"   CV Job Search Test: {'✅ PASSED' if cv_test_passed else '❌ FAILED'}")
    print(f"   Intent Distinction Test: {'✅ PASSED' if distinction_test_passed else '❌ FAILED'}")
    
    overall_success = cv_test_passed and distinction_test_passed
    
    if overall_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ CV-based job search queries are correctly classified as 'search_jobs'")
        print("✅ Intent distinction between search_jobs and filter_jobs is working")
        print("✅ Vector chat and regular chat should have consistent intent classification")
    else:
        print("\n🔧 ISSUES FOUND:")
        if not cv_test_passed:
            print("❌ CV-based job search queries are not being classified correctly")
        if not distinction_test_passed:
            print("❌ Intent distinction between search_jobs and filter_jobs needs improvement")
        
        print("\n💡 Recommendations:")
        print("1. Review and enhance intent descriptions in vector_intent_detector.py")
        print("2. Add more CV-specific keywords to 'search_jobs' intent")
        print("3. Improve distinction between 'search_jobs' and 'filter_jobs'")
        print("4. Consider adjusting confidence thresholds")
    
    return overall_success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
