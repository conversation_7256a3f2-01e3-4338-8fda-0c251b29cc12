# 🚀 FOIS Chatbot Setup Guide

## 📋 Quick Start

### 1. Basic Installation
```bash
# Clone the repository
git clone <repository-url>
cd chatbox-ai

# Install core dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your GEMINI_API_KEY
```

### 2. Running the Chatbot

#### Option A: Development Mode (Recommended for testing)
```bash
# Standard development with auto-reload (will restart once)
python web_app.py --port 5001 --debug

# Optimized development without auto-reload (faster startup)
python run_optimized.py --port 5001 --debug
```

#### Option B: Production Mode (Recommended for deployment)
```bash
# Install production server
pip install waitress

# Run in production mode
python run_optimized.py --port 5001 --production
```

## ⚠️ Common Warnings and Solutions

### 1. CV Processing Libraries Warning
```
⚠️ Some CV processing libraries not installed. Install with:
pip install PyPDF2 python-docx Pillow python-magic
```

**What it means:** CV upload features are disabled
**When to fix:** Only if you need CV upload functionality
**How to fix:**
```bash
pip install PyPDF2 python-docx Pillow python-magic
```

### 2. Development Server Warning
```
WARNING: This is a development server. Do not use it in a production deployment. 
Use a production WSGI server instead.
```

**What it means:** Flask's built-in server is not production-ready
**When to fix:** Only for production deployment
**How to fix:**
```bash
# Install production server
pip install waitress

# Run in production mode
python run_optimized.py --port 5001 --production
```

## 🎯 Feature Matrix

| Feature | Core Required | Optional Libraries | Notes |
|---------|---------------|-------------------|-------|
| Vector Chatbot | ✅ | - | Main functionality |
| Text Chat | ✅ | - | Basic chat features |
| CV Upload | ❌ | PyPDF2, python-docx, Pillow, python-magic | File processing |
| Production Server | ❌ | waitress | WSGI server |
| Image Processing | ❌ | opencv-python | Enhanced CV analysis |

## 🔧 Running Modes Comparison

| Mode | Command | Startup Time | Warnings | Best For |
|------|---------|--------------|----------|----------|
| **Standard Dev** | `python web_app.py --debug` | ~6s | Flask dev warning | Development with file watching |
| **Optimized Dev** | `python run_optimized.py --debug` | ~3s | Flask dev warning | Development without file watching |
| **Production** | `python run_optimized.py --production` | ~2s | None | Production deployment |

## 📦 Installation Options

### Minimal Installation (Core Features Only)
```bash
pip install flask flask-cors google-generativeai python-dotenv numpy
```

### Standard Installation (Recommended)
```bash
pip install -r requirements.txt
```

### Production Installation (All Features)
```bash
pip install -r requirements.txt
# Uncomment optional packages in requirements.txt as needed
```

## 🌐 API Endpoints

### Core Endpoints
- `/` - Main chatbot interface
- `/modern` - Modern UI with vector toggle
- `/api/chat` - Regular chat API
- `/api/chat-vector` - Vector-based chat API
- `/api/vector-analysis` - Intent analysis (debugging)

### Optional Endpoints (require CV libraries)
- `/api/upload-cv` - CV upload and analysis

## 🎛️ Configuration

### Environment Variables (.env)
```bash
GEMINI_API_KEY=your_gemini_api_key_here
FLASK_SECRET_KEY=your_secret_key_here
```

### Runtime Options
```bash
# Development
python run_optimized.py --host 0.0.0.0 --port 5001 --debug

# Production
python run_optimized.py --host 0.0.0.0 --port 5001 --production

# Custom host/port
python run_optimized.py --host 127.0.0.1 --port 8080
```

## 🚀 Deployment

### Local Development
```bash
python run_optimized.py --debug
```

### Production Server
```bash
# Install production dependencies
pip install waitress

# Run production server
python run_optimized.py --production --port 80
```

### Docker Deployment
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5001
CMD ["python", "run_optimized.py", "--production", "--port", "5001"]
```

## 🔍 Troubleshooting

### Vector System Slow Startup
- **Cause:** Multiple initializations in debug mode
- **Solution:** Use `python run_optimized.py` instead of `python web_app.py`

### CV Upload Not Working
- **Cause:** Missing CV processing libraries
- **Solution:** `pip install PyPDF2 python-docx Pillow python-magic`

### Production Warnings
- **Cause:** Using Flask development server
- **Solution:** Use `--production` flag with Waitress

### Port Already in Use
- **Cause:** Another service using the port
- **Solution:** Use different port with `--port 5002`

## 📊 Performance Tips

1. **Use optimized runner:** `python run_optimized.py`
2. **Disable auto-reload in production:** `--production` flag
3. **Install optional libraries only if needed**
4. **Use production WSGI server for deployment**
5. **Monitor vector system initialization logs**

## 🎉 Success Indicators

When everything is working correctly, you should see:
```
🚀 Starting OPTIMIZED FOIS Chatbot Web Interface (DEVELOPMENT)...
🏢 Company: FOIS GROUP - FOIS ICT PRO
🌐 Access at: http://localhost:5001
🔧 Debug mode: ON
🎯 New Intent System: ACTIVE
⚡ Optimization: NO AUTO-RELOAD (single initialization)
```

No CV warnings (unless you need CV features)
No production warnings (in production mode)
Fast startup (~2-3 seconds)
