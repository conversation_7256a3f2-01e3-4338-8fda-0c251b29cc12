#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Vector-based Chatbot Router
Implements the 6-step vector-based chatbot process:
1. Receive user input
2. Calculate user input vector
3. Calculate similarities between user input vector and user intents
4. Find the user_intent with the highest score
5. If user_intent belongs to the group that needs feedback -> call directly through the feedback function
6. Otherwise: call new_response_generator_vector generate_response as before
"""

from sample_data import (
    JOB_MARKET_INSIGHTS_2024, SALARY_RANGES, SAMPLE_JOBS, FEEDBACK_RESPONSES,
    HOT_TECH_STACK_2024, IT_MARKET_TRENDS_2024_2025, FREELANCE_MARKET_2024,
    IT_FORECAST_2025
)
from new_response_generator import NewResponseGenerator
from gemini_ai import <PERSON><PERSON><PERSON>
from intent_integration import IntentIntegration
from vector_intent_detector import VectorIntentDetector
import numpy as np
from typing import Dict, List, Tuple, Optional
from datetime import datetime
import sys
import os

# Add parent directory to path to import existing modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class VectorChatbotRouter:
    """
    Main router for vector-based chatbot system
    Handles the complete 6-step process for intent detection and response generation
    """

    # Class-level shared instances to avoid multiple initializations
    _shared_vector_detector = None
    _shared_intent_integration = None
    _shared_gemini_ai = None
    _shared_response_generator = None

    def __init__(self):
        """Initialize the vector chatbot router with shared instances"""
        print("🚀 Initializing Vector Chatbot Router...")

        # Use shared instances to avoid multiple initializations
        if VectorChatbotRouter._shared_vector_detector is None:
            print("📦 Creating shared vector detector instance...")
            VectorChatbotRouter._shared_vector_detector = VectorIntentDetector()

        if VectorChatbotRouter._shared_intent_integration is None:
            print("📦 Creating shared intent integration instance...")
            VectorChatbotRouter._shared_intent_integration = IntentIntegration()

        if VectorChatbotRouter._shared_gemini_ai is None:
            print("📦 Creating shared Gemini AI instance...")
            VectorChatbotRouter._shared_gemini_ai = GeminiAI()

        if VectorChatbotRouter._shared_response_generator is None:
            print("📦 Creating shared response generator instance...")
            VectorChatbotRouter._shared_response_generator = NewResponseGenerator(
                VectorChatbotRouter._shared_gemini_ai)

        # Reference shared instances
        self.vector_detector = VectorChatbotRouter._shared_vector_detector
        self.intent_integration = VectorChatbotRouter._shared_intent_integration
        self.gemini_ai = VectorChatbotRouter._shared_gemini_ai
        self.response_generator = VectorChatbotRouter._shared_response_generator

        # Define intents that need feedback (step 5)
        self.feedback_required_intents = {
            'search_jobs',
            'filter_jobs',
            'salary_query',
            'ask_company_info',
            'cv_feedback',
            'upload_resume',
            'update_resume',
            'job_it_trending',
        }

        print("✅ Vector Chatbot Router initialized successfully")

    def _get_language_instruction(self, language: str) -> str:
        """Get language instruction for prompts based on language code"""
        if language.startswith('en'):
            return "English"
        elif language.startswith('ja'):
            return "Japanese"
        else:
            return "Vietnamese"

    def process_user_input(self, user_input: str, user_id: str, conversation_history: List[Dict] = None, language: str = 'vi') -> Dict:
        """
        Main method implementing the 6-step vector-based chatbot process

        Args:
            user_input: The user's message
            user_id: Unique identifier for the user
            conversation_history: Previous conversation context

        Returns:
            Dict containing response data and metadata
        """
        if conversation_history is None:
            conversation_history = []

        print(f"\n🎯 Vector Chatbot Processing: '{user_input}'")
        print("=" * 50)

        try:
            # Step 1: Receive user input (already done)
            print("📝 Step 1: User input received")

            # Step 2: Calculate user input vector
            print("🔢 Step 2: Calculating user input vector...")
            user_vector = self._calculate_user_input_vector(user_input)

            # Step 3: Calculate similarities between user input vector and user intents
            print("📊 Step 3: Calculating intent similarities...")
            similarities = self._calculate_intent_similarities(user_vector)

            # Step 4: Find the user_intent with the highest score
            print("🎯 Step 4: Finding highest scoring intent...")
            best_intent, confidence, all_similarities = self._find_highest_scoring_intent(
                similarities, user_input)

            print(
                f"   Best Intent: {best_intent} (confidence: {confidence:.4f})")

            # Step 5: Check if user_intent belongs to the group that needs feedback
            print("🔍 Step 5: Checking if feedback is required...")
            needs_feedback = self._check_feedback_requirement(best_intent)

            if needs_feedback:
                print("📞 Routing to feedback function...")
                return self._handle_feedback_intent(user_input, user_id, conversation_history, best_intent, confidence)

            # Step 6: Otherwise use new_response_generator_vector
            print("🤖 Step 6: Using vector response generator...")
            return self._generate_vector_response(user_input, user_id, conversation_history, best_intent, confidence, all_similarities)

        except Exception as e:
            print(f"❌ Error in vector chatbot processing: {str(e)}")
            return self._generate_fallback_response(user_input, user_id, conversation_history)

    def _calculate_user_input_vector(self, user_input: str) -> np.ndarray:
        """Step 2: Calculate user input vector using Gemini embeddings"""
        try:
            user_vector = self.vector_detector.get_embedding(user_input)
            print(f"   Vector shape: {user_vector.shape}")
            return user_vector
        except Exception as e:
            print(f"   ⚠️ Error calculating vector: {e}")
            # Return zero vector as fallback
            return np.zeros(768)

    def _calculate_intent_similarities(self, user_vector: np.ndarray) -> Dict[str, float]:
        """Step 3: Calculate similarities between user vector and all intent vectors"""
        similarities = {}

        for intent, intent_vector in self.vector_detector.intent_vectors.items():
            similarity = self.vector_detector.cosine_similarity_score(
                user_vector, intent_vector)
            similarities[intent] = similarity

        # Sort and display top 3 similarities
        sorted_similarities = sorted(
            similarities.items(), key=lambda x: x[1], reverse=True)
        print("   Top 3 similarities:")
        for i, (intent, score) in enumerate(sorted_similarities[:3]):
            print(f"     {i+1}. {intent}: {score:.4f}")

        return similarities

    def _find_highest_scoring_intent(self, similarities: Dict[str, float], user_input: str = "") -> Tuple[str, float, Dict[str, float]]:
        """Step 4: Find the intent with the highest similarity score"""
        best_intent = max(similarities, key=similarities.get)
        confidence = similarities[best_intent]

        # Special case for Vietnamese company-specific job search queries
        # If query mentions "Fois" or "công ty" and has job-related terms, boost search_jobs intent
        if user_input and isinstance(user_input, str):
            user_input_lower = user_input.lower()
            company_terms = ["fois", "công ty", "company"]
            job_terms = ["vị trí", "việc làm", "job", "công việc", "phù hợp"]

            has_company_term = any(term in user_input_lower for term in company_terms)
            has_job_term = any(term in user_input_lower for term in job_terms)

            if has_company_term and has_job_term:
                print(f"   📊 Company-specific job search detected in: '{user_input}'")

                # If search_jobs is close to the best intent, prefer it
                search_jobs_sim = similarities.get("search_jobs", 0)
                best_sim = similarities.get(best_intent, 0)

                if search_jobs_sim > 0 and (best_sim - search_jobs_sim) < 0.1:
                    print(f"   🔄 Boosting 'search_jobs' intent (sim: {search_jobs_sim:.3f}) over '{best_intent}' (sim: {best_sim:.3f})")
                    best_intent = "search_jobs"
                    confidence = search_jobs_sim

        # Apply dynamic threshold based on intent type
        # Lower threshold for job-related intents to improve detection, especially for Vietnamese queries
        job_related_intents = ["search_jobs", "filter_jobs", "cv_feedback", "salary_query", "job_it_trending"]

        if best_intent in job_related_intents:
            threshold = 0.2   # Lower threshold for job-related queries (improved for Vietnamese)
        else:
            threshold = 0.25  # Lower standard threshold for better Vietnamese support

        print(f"   Using threshold: {threshold} for intent: {best_intent}")

        if confidence < threshold:
            print(f"   Confidence {confidence:.3f} below threshold {threshold}, classifying as 'other'")
            best_intent = "other"
            confidence = similarities.get("other", 0.0)

        return best_intent, confidence, similarities

    def _check_feedback_requirement(self, intent: str) -> bool:
        """Step 5: Check if the intent requires feedback handling"""
        # Map vector intent to response generator intent if needed
        mapped_intent = self.intent_integration.intent_mapping.get(
            intent, intent)

        requires_feedback = mapped_intent in self.feedback_required_intents
        print(f"   Intent: {intent} -> {mapped_intent}")
        print(f"   Requires feedback: {requires_feedback}")

        return requires_feedback

    def _handle_feedback_intent(self, user_input: str, user_id: str, conversation_history: List[Dict],
                                intent: str, confidence: float) -> Dict:
        """Handle intents that require feedback using generate_prompt_based_on_user_intent"""
        print(
            f"🔄 Handling feedback intent: {intent} with prompt-based response generation")

        # Map to response generator intent
        mapped_intent = self.intent_integration.intent_mapping.get(
            intent, intent)

        try:
            # generate_thinking_message_response automatically calls generate_prompt_based_on_user_intent
            # internally based on the user_intent parameter, so we get specialized prompts automatically
            response_data = self.response_generator.generate_thinking_message_response(
                user_id, user_input, conversation_history, mapped_intent
            )

            # Add vector-specific metadata
            response_data.update({
                'vector_intent': intent,
                'vector_confidence': confidence,
                'processing_method': 'prompt_based_thinking_response',
                'vector_routing': True,
                'data_source': 'generate_prompt_based_on_user_intent',
                'mapped_intent': mapped_intent
            })

            return response_data

        except Exception as e:
            print(f"❌ Error in feedback handling: {e}")
            return self._generate_fallback_response(user_input, user_id, conversation_history)

    def _generate_vector_response(self, user_input: str, user_id: str, conversation_history: List[Dict],
                                  intent: str, confidence: float, similarities: Dict[str, float]) -> Dict:
        """Step 6: Generate response using vector-enhanced response generator"""
        print(f"🎨 Generating vector response for intent: {intent}")

        # Map to response generator intent
        mapped_intent = self.intent_integration.intent_mapping.get(
            intent, intent)

        try:
            # Use the regular response generator but with vector intent information
            response_data = self.response_generator.generate_response(
                user_id, user_input, conversation_history
            )

            # Enhance with vector-specific information
            response_data.update({
                'vector_intent': intent,
                'vector_confidence': confidence,
                'mapped_intent': mapped_intent,
                'intent_similarities': similarities,
                'processing_method': 'vector_response_generator',
                'vector_routing': True
            })

            return response_data

        except Exception as e:
            print(f"❌ Error in vector response generation: {e}")
            return self._generate_fallback_response(user_input, user_id, conversation_history)

    def _generate_fallback_response(self, user_input: str, user_id: str, conversation_history: List[Dict]) -> Dict:
        """Generate fallback response when vector processing fails"""
        print("🔄 Generating fallback response...")

        try:
            # Use the standard response generator as fallback
            response_data = self.response_generator.generate_response(
                user_id, user_input, conversation_history
            )

            response_data.update({
                'vector_intent': 'FALLBACK',
                'vector_confidence': 0.0,
                'processing_method': 'fallback',
                'vector_routing': False
            })

            return response_data

        except Exception as e:
            print(f"❌ Even fallback failed: {e}")
            return {
                'message': 'Xin lỗi, tôi đang gặp sự cố kỹ thuật. Vui lòng thử lại sau.',
                'response_type': 'error',
                'tone': 'apologetic',
                'emotion': 'concerned',
                'user_intent': 'error',
                'suggestion_answers': [],
                'contextual_followup': {},
                'vector_intent': 'ERROR',
                'vector_confidence': 0.0,
                'processing_method': 'error_fallback',
                'vector_routing': False,
                'timestamp': datetime.now().isoformat()
            }

    def get_intent_analysis(self, user_input: str) -> Dict:
        """Get detailed analysis of intent detection for debugging"""
        try:
            user_vector = self._calculate_user_input_vector(user_input)
            similarities = self._calculate_intent_similarities(user_vector)
            best_intent, confidence, _ = self._find_highest_scoring_intent(
                similarities, user_input)
            needs_feedback = self._check_feedback_requirement(best_intent)

            return {
                'user_input': user_input,
                'vector_intent': best_intent,
                'confidence': confidence,
                'needs_feedback': needs_feedback,
                'mapped_intent': self.intent_integration.intent_mapping.get(best_intent, best_intent),
                'all_similarities': similarities,
                'top_3_similarities': sorted(similarities.items(), key=lambda x: x[1], reverse=True)[:3],
                'processing_method': 'feedback_function' if needs_feedback else 'vector_response_generator'
            }

        except Exception as e:
            return {
                'error': str(e),
                'user_input': user_input
            }

    def _build_job_context_prompt(self, jobs: List[Dict], market_insights: List[Dict]) -> str:
        """Build detailed job context for Gemini prompt"""
        context_parts = []

        # Add job details
        context_parts.append("📋 **AVAILABLE JOB OPPORTUNITIES:**")
        for i, job in enumerate(jobs, 1):
            hot_tag = job.get('hot_tag', '')
            benefits = job.get('benefits', [])

            context_parts.append(f"\n**{i}. {job['title']} {hot_tag}**")
            context_parts.append(f"   • Company: {job['company']}")
            context_parts.append(f"   • Salary: {job['salary_range']}")
            context_parts.append(f"   • Location: {job['location']}")
            context_parts.append(f"   • Experience: {job['experience']}")
            context_parts.append(f"   • Skills: {', '.join(job['skills'])}")
            context_parts.append(
                f"   • Remote: {'Yes' if job['remote'] else 'No'}")
            context_parts.append(f"   • Description: {job['description']}")

            if job.get('requirements'):
                context_parts.append(
                    f"   • Requirements: {'; '.join(job['requirements'])}")

            if benefits:
                context_parts.append(f"   • Benefits: {'; '.join(benefits)}")

        # Add market insights
        context_parts.append("\n📈 **MARKET INSIGHTS 2024-2025:**")
        for insight in market_insights:
            context_parts.append(
                f"• **{insight['title']}** ({insight['demand_level']})")
            context_parts.append(
                f"  - Average Salary: {insight['avg_salary']}")
            context_parts.append(
                f"  - Key Skills: {', '.join(insight['key_skills'])}")
            context_parts.append(f"  - Market Note: {insight['market_note']}")

        return '\n'.join(context_parts)

    def _build_salary_context_prompt(self, salary_data: Dict, market_positions: List[Dict]) -> str:
        """Build detailed salary context for Gemini prompt"""
        context_parts = []

        # Add salary ranges by level
        context_parts.append("💰 **SALARY RANGES BY LEVEL:**")
        for level, data in salary_data.items():
            level_name = level.replace('_', ' ').title()
            context_parts.append(f"\n**{level_name}:**")
            context_parts.append(f"   • Range: {data['range']}")
            context_parts.append(f"   • Description: {data['description']}")
            context_parts.append(f"   • Skills: {', '.join(data['skills'])}")
            context_parts.append(f"   • Market Trend: {data['market_trend']}")

            if 'experience_salary' in data:
                context_parts.append(
                    f"   • Experience-based: {data['experience_salary']}")

        # Add hot positions
        context_parts.append("\n🔥 **HOT POSITIONS 2024:**")
        for pos in market_positions:
            context_parts.append(
                f"• **{pos['title']}**: {pos['avg_salary']} ({pos['demand_level']})")
            context_parts.append(f"  - Skills: {', '.join(pos['key_skills'])}")
            context_parts.append(f"  - Note: {pos['market_note']}")

        return '\n'.join(context_parts)

    def _generate_fallback_job_response(self, jobs: List[Dict], market_insights: List[Dict], intent: str) -> Dict:
        """Fallback response when Gemini fails"""
        message_parts = [
            "🎯 **Cơ hội việc làm IT hot nhất 2024-2025:**\n"
        ]

        for i, job in enumerate(jobs, 1):
            hot_tag = job.get('hot_tag', '')
            message_parts.append(f"**{i}. {job['title']} {hot_tag}**")
            message_parts.append(f"   💰 Lương: {job['salary_range']}")
            message_parts.append(f"   📍 Địa điểm: {job['location']}")
            message_parts.append(
                f"   🔧 Skills: {', '.join(job['skills'][:3])}")
            message_parts.append(f"   📝 {job['description'][:100]}...\n")

        return {
            'message': '\n'.join(message_parts),
            'response_type': 'job_opportunities',
            'tone': 'professional',
            'emotion': 'helpful',
            'user_intent': intent,
            'data_source': 'SAMPLE_JOBS (fallback)'
        }

    def _build_cv_context_prompt(self, jobs: List[Dict], market_insights: List[Dict]) -> str:
        """Build detailed CV guidance context for Gemini prompt"""
        context_parts = []

        # Extract all skills from jobs
        all_skills = set()
        for job in jobs:
            all_skills.update(job['skills'])

        # Add skills analysis
        context_parts.append("🔧 **REQUIRED SKILLS FROM JOB MARKET:**")
        for skill in sorted(all_skills):
            context_parts.append(f"• {skill}")

        # Add job requirements
        context_parts.append("\n📋 **JOB REQUIREMENTS BY POSITION:**")
        for job in jobs:
            hot_tag = job.get('hot_tag', '')
            context_parts.append(f"\n**{job['title']} {hot_tag}**")
            context_parts.append(f"   • Salary: {job['salary_range']}")
            context_parts.append(f"   • Experience: {job['experience']}")
            context_parts.append(
                f"   • Key Skills: {', '.join(job['skills'])}")
            if job.get('requirements'):
                context_parts.append(
                    f"   • Requirements: {'; '.join(job['requirements'])}")

        # Add market insights for CV optimization
        context_parts.append("\n📈 **MARKET TRENDS FOR CV OPTIMIZATION:**")
        for insight in market_insights[:3]:
            context_parts.append(
                f"• **{insight['title']}** - {insight['demand_level']}")
            context_parts.append(
                f"  - Skills to highlight: {', '.join(insight['key_skills'])}")
            context_parts.append(f"  - Market note: {insight['market_note']}")

        return '\n'.join(context_parts)

    def _generate_fallback_salary_response(self, salary_data: Dict, market_positions: List[Dict], intent: str) -> Dict:
        """Fallback salary response when Gemini fails"""
        message_parts = [
            "💰 **Mức lương IT Việt Nam 2024-2025:**\n"
        ]

        # Add salary ranges
        for level, data in list(salary_data.items())[:3]:
            level_name = level.replace('_', ' ').title()
            message_parts.append(f"**{level_name}:**")
            message_parts.append(f"   💵 {data['range']}")
            message_parts.append(f"   📝 {data['description']}")
            message_parts.append(f"   📈 {data['market_trend']}\n")

        return {
            'message': '\n'.join(message_parts),
            'response_type': 'salary_information',
            'tone': 'informative',
            'emotion': 'helpful',
            'user_intent': intent,
            'data_source': 'SALARY_RANGES (fallback)'
        }

    def _generate_fallback_cv_response(self, jobs: List[Dict], intent: str) -> Dict:
        """Fallback CV response when Gemini fails"""
        all_skills = set()
        for job in jobs:
            all_skills.update(job['skills'])

        message_parts = [
            "📄 **Hướng dẫn tối ưu CV cho thị trường IT 2024-2025:**\n",
            "🔧 **Skills hot nhất cần có trong CV:**"
        ]

        for skill in list(all_skills)[:8]:
            message_parts.append(f"• {skill}")

        return {
            'message': '\n'.join(message_parts),
            'response_type': 'cv_guidance',
            'tone': 'advisory',
            'emotion': 'supportive',
            'user_intent': intent,
            'data_source': 'SAMPLE_JOBS (fallback)'
        }

    def _generate_job_feedback_response(self, user_input: str, user_id: str, conversation_history: List[Dict], intent: str) -> Dict:
        """Generate job-related feedback response using comprehensive sample_data like regular chat"""
        print(
            f"📊 Generating job feedback using comprehensive sample_data as Gemini prompt context for intent: {intent}")

        # Get ALL relevant data from sample_data (same as regular chat)
        relevant_jobs = SAMPLE_JOBS  # Use ALL jobs, not just top 3
        market_insights = JOB_MARKET_INSIGHTS_2024["most_demanded_positions"]

        # Create comprehensive market data context (same as regular chat)
        comprehensive_market_context = self._build_comprehensive_market_context()

        # Create Gemini prompt with comprehensive data as context
        gemini_prompt = f"""
{self.response_generator.prompt_config}

🎯 **SPECIALIZED ROLE: IT Market Analyst & Career Advisor**

You are now an expert IT market analyst with access to the latest Vietnam IT market data from 2024-2025 reports (ITviec, TopDev, Navigos). Your mission is to provide accurate, data-driven insights about:

1. 🔥 Hot technology trends and salary ranges
2. 📈 Market demand and growth sectors
3. 💰 Salary insights and career opportunities
4. 🚀 Future predictions and recommendations

**COMPREHENSIVE MARKET DATA:**
{comprehensive_market_context}

🎯 **USER REQUEST:** {user_input}

**INSTRUCTIONS:**
- Use the provided job data and market insights to answer the user's question
- Maintain your friendly, professional personality
- Include specific job details from the context data
- Add market trends and salary information
- Provide actionable advice
- Use emojis and formatting for engagement
- End with relevant follow-up questions

**RESPONSE LANGUAGE:** Vietnamese (unless user asks in English)
"""

        try:
            # Generate response using Gemini with sample data context
            gemini_response = self.response_generator.gemini_ai.generate_text(
                gemini_prompt)

            return {
                'message': gemini_response,
                'response_type': 'job_opportunities',
                'tone': 'professional',
                'emotion': 'enthusiastic',
                'user_intent': intent,
                'suggestion_answers': [
                    "Tôi muốn biết thêm về vị trí AI/ML Engineer",
                    "Lương Python Developer như thế nào?",
                    "Có job remote không?",
                    "Yêu cầu kinh nghiệm cho fresh graduate?"
                ],
                'contextual_followup': {
                    'job_data': relevant_jobs,
                    'market_insights': market_insights,
                    'data_source': 'SAMPLE_JOBS + JOB_MARKET_INSIGHTS_2024 + Gemini AI',
                    'generation_method': 'gemini_with_sample_data_context'
                }
            }

        except Exception as e:
            print(f"❌ Error generating Gemini response: {e}")
            # Fallback to direct sample data response
            return self._generate_fallback_job_response(relevant_jobs, market_insights, intent)

    def _generate_salary_feedback_response(self, user_input: str, user_id: str, conversation_history: List[Dict], intent: str) -> Dict:
        """Generate salary feedback response using comprehensive salary data like regular chat"""
        print(
            f"💰 Generating salary feedback using comprehensive sample_data as Gemini prompt context for intent: {intent}")

        # Get comprehensive salary data from sample_data (same as regular chat)
        salary_data = SALARY_RANGES
        market_positions = JOB_MARKET_INSIGHTS_2024["most_demanded_positions"]

        # Create comprehensive market data context (same as regular chat)
        comprehensive_market_context = self._build_comprehensive_market_context()

        # Create Gemini prompt with comprehensive salary data as context
        gemini_prompt = f"""
{self.response_generator.prompt_config}

🎯 **SPECIALIZED ROLE: IT Salary Analyst & Career Advisor**

You are now an expert IT salary analyst with access to the latest Vietnam IT salary data from 2024-2025 reports (ITviec, TopDev, Navigos). Your mission is to provide accurate, data-driven salary insights about:

1. 💰 Salary ranges by position and experience level
2. 📈 Market trends and growth predictions
3. 🔥 Hot technologies and their compensation
4. 🚀 Career advancement and salary growth paths

**COMPREHENSIVE SALARY & MARKET DATA:**
{comprehensive_market_context}

🎯 **USER REQUEST:** {user_input}

**INSTRUCTIONS:**
- Use the provided salary data and market insights to answer the user's question
- Maintain your friendly, professional personality
- Include specific salary ranges and market trends from the context data
- Provide career advice and growth paths
- Compare different levels and positions
- Use emojis and formatting for engagement
- End with relevant follow-up questions

**RESPONSE LANGUAGE:** Vietnamese (unless user asks in English)
"""

        try:
            # Generate response using Gemini with salary data context
            gemini_response = self.response_generator.gemini_ai.generate_text(
                gemini_prompt)

            return {
                'message': gemini_response,
                'response_type': 'salary_information',
                'tone': 'informative',
                'emotion': 'helpful',
                'user_intent': intent,
                'suggestion_answers': [
                    "Lương AI/ML Engineer bao nhiêu?",
                    "Fresh graduate có mức lương như thế nào?",
                    "Vị trí nào lương cao nhất?",
                    "Xu hướng lương IT 2025?"
                ],
                'contextual_followup': {
                    'salary_data': salary_data,
                    'hot_positions': market_positions,
                    'data_source': 'SALARY_RANGES + JOB_MARKET_INSIGHTS_2024 + Gemini AI',
                    'generation_method': 'gemini_with_salary_data_context'
                }
            }

        except Exception as e:
            print(f"❌ Error generating Gemini salary response: {e}")
            # Fallback to direct sample data response
            return self._generate_fallback_salary_response(salary_data, market_positions, intent)

    def _generate_cv_feedback_response(self, user_input: str, user_id: str, conversation_history: List[Dict], intent: str, language: str = 'vi') -> Dict:
        """Generate CV feedback response using comprehensive job requirements from sample data"""
        print(
            f"📄 Generating CV feedback using comprehensive sample_data as Gemini prompt context for intent: {intent}")

        # Get ALL job requirements from sample data for CV guidance (same as regular chat)
        sample_jobs = SAMPLE_JOBS  # Use ALL jobs, not just top 3
        market_insights = JOB_MARKET_INSIGHTS_2024["most_demanded_positions"]

        # Create detailed prompt with CV guidance context
        cv_context = self._build_cv_context_prompt(
            sample_jobs, market_insights)

        # Create Gemini prompt with CV guidance context
        gemini_prompt = f"""
{self.response_generator.prompt_config}

🎯 **CONTEXT: CV Guidance & Job Requirements**
{cv_context}

🎯 **USER REQUEST:** {user_input}

**INSTRUCTIONS:**
- Use the provided job requirements and market data to give CV advice
- Maintain your friendly, supportive personality
- Include specific skills and requirements from the context data
- Provide actionable CV improvement tips
- Suggest portfolio elements and project ideas
- Use emojis and formatting for engagement
- End with relevant follow-up questions

**RESPONSE LANGUAGE:** {self._get_language_instruction(language)}
"""

        try:
            # Generate response using Gemini with CV guidance context
            gemini_response = self.response_generator.gemini_ai.generate_text(
                gemini_prompt)

            # Extract skills for metadata
            all_skills = set()
            for job in sample_jobs:
                all_skills.update(job['skills'])

            return {
                'message': gemini_response,
                'response_type': 'cv_guidance',
                'tone': 'advisory',
                'emotion': 'supportive',
                'user_intent': intent,
                'suggestion_answers': [
                    "Tôi muốn upload CV để review",
                    "Skills nào quan trọng nhất cho Python dev?",
                    "Làm sao để CV nổi bật?",
                    "Portfolio cần có những gì?"
                ],
                'contextual_followup': {
                    'required_skills': list(all_skills),
                    'job_requirements': [job['requirements'] for job in sample_jobs],
                    'data_source': 'SAMPLE_JOBS + JOB_MARKET_INSIGHTS_2024 + Gemini AI',
                    'generation_method': 'gemini_with_cv_data_context'
                }
            }

        except Exception as e:
            print(f"❌ Error generating Gemini CV response: {e}")
            # Fallback to direct sample data response
            return self._generate_fallback_cv_response(sample_jobs, intent)

    def _generate_comprehensive_cv_response(self, user_input: str, user_id: str, conversation_history: List[Dict], intent: str, language: str = 'vi') -> Dict:
        """Generate comprehensive CV response using the full response generator with language support"""
        print(
            f"📄 Generating COMPREHENSIVE CV response using generate_prompt_based_on_user_intent for intent: {intent} (language: {language})")

        try:
            # Create a language-aware prompt by modifying the user input
            language_instruction = self._get_language_instruction(language)
            enhanced_user_input = f"""
LANGUAGE INSTRUCTION: Please respond in {language_instruction}.

USER REQUEST: {user_input}

CONTEXT: This is a CV/resume analysis request. Please provide a comprehensive, detailed response with:
1. Detailed CV analysis and feedback
2. Specific job recommendations from FOIS ICT PRO
3. Actionable improvement suggestions
4. Market insights and salary information
5. Next steps for the candidate

Respond in {language_instruction} language throughout the entire response.
"""

            # Use the comprehensive response generator
            response_data = self.response_generator.generate_response(
                user_id, enhanced_user_input, conversation_history
            )

            # Add vector-specific metadata
            response_data.update({
                'vector_intent': intent,
                'vector_confidence': 0.9,  # High confidence for CV content
                'processing_method': 'comprehensive_cv_response_with_language_support',
                'vector_routing': True,
                'language': language,
                'data_source': 'generate_prompt_based_on_user_intent + SAMPLE_JOBS + language_enhancement'
            })

            return response_data

        except Exception as e:
            print(f"❌ Error generating comprehensive CV response: {e}")
            # Fallback to the partial CV response
            return self._generate_cv_feedback_response(user_input, user_id, conversation_history, intent, language)

    def _build_comprehensive_market_context(self) -> str:
        """Build comprehensive market context exactly like regular chat"""
        return f"""
📊 **VIETNAM IT MARKET DATA 2024-2025** (From ITviec, TopDev, Navigos Reports):

🔥 **HOT TECHNOLOGIES & SALARY RANGES:**
• AI/ML: {HOT_TECH_STACK_2024['ai_ml']['salary_range']} - {HOT_TECH_STACK_2024['ai_ml']['note']}
• Python: {HOT_TECH_STACK_2024['backend']['salary_range']} - {HOT_TECH_STACK_2024['backend']['note']}
• Cloud/DevOps: {HOT_TECH_STACK_2024['cloud_devops']['salary_range']} - {HOT_TECH_STACK_2024['cloud_devops']['note']}
• Blockchain: {HOT_TECH_STACK_2024['blockchain']['salary_range']} - {HOT_TECH_STACK_2024['blockchain']['note']}
• Frontend: {HOT_TECH_STACK_2024['frontend']['salary_range']} - {HOT_TECH_STACK_2024['frontend']['note']}
• Mobile: {HOT_TECH_STACK_2024['mobile']['salary_range']} - {HOT_TECH_STACK_2024['mobile']['note']}

💰 **TOP PAYING POSITIONS:**
• AI/ML Engineer: {SALARY_RANGES['ai_ml_engineer']['range']} - {SALARY_RANGES['ai_ml_engineer']['market_trend']}
• Python Developer: {SALARY_RANGES['python_developer']['range']} - {SALARY_RANGES['python_developer']['market_trend']}
• Blockchain Engineer: {SALARY_RANGES['blockchain_engineer']['range']} - {SALARY_RANGES['blockchain_engineer']['market_trend']}
• DevOps Engineer: {SALARY_RANGES['devops_engineer']['range']} - {SALARY_RANGES['devops_engineer']['market_trend']}

🚀 **MARKET TRENDS:**
• Status: {IT_MARKET_TRENDS_2024_2025['overview']['status']}
• Key Drivers: {', '.join(IT_MARKET_TRENDS_2024_2025['overview']['key_drivers'])}
• Challenges: {', '.join(IT_MARKET_TRENDS_2024_2025['overview']['challenges'])}

💼 **FREELANCE MARKET:**
• AI/ML Projects: {FREELANCE_MARKET_2024['top_earning_categories']['ai_ml_development']['avg_monthly']} (highest)
• Web Development: {FREELANCE_MARKET_2024['top_earning_categories']['web_development']['avg_monthly']}
• Mobile Development: {FREELANCE_MARKET_2024['top_earning_categories']['mobile_development']['avg_monthly']}

🎯 **MOST DEMANDED POSITIONS 2024:**
{chr(10).join([f"• {pos['title']}: {pos['avg_salary']} - {pos['market_note']}" for pos in JOB_MARKET_INSIGHTS_2024['most_demanded_positions']])}

⚠️ **HIRING CHALLENGES:**
{chr(10).join([f"• {challenge}" for challenge in JOB_MARKET_INSIGHTS_2024['hiring_challenges']])}

🔮 **2025 FORECAST:**
• Growth Sectors: {', '.join(IT_FORECAST_2025['growth_sectors'])}
• Skill Demands: {', '.join(IT_FORECAST_2025['skill_demands'])}
• Market Outlook: {IT_FORECAST_2025['market_outlook']}

🔥 **HOT JOB OPPORTUNITIES AT FOIS ICT PRO:**
{chr(10).join([f"• {job['title']} {job.get('hot_tag', '')}: {job['salary_range']} - {job['location']}" for job in SAMPLE_JOBS])}

💡 **SALARY PREDICTIONS 2025:**
• AI/ML Engineer: {IT_FORECAST_2025['salary_predictions']['ai_ml_engineer']}
• Senior Python Dev: {IT_FORECAST_2025['salary_predictions']['senior_python_dev']}
• Cloud Architect: {IT_FORECAST_2025['salary_predictions']['cloud_architect']}
• Blockchain Dev: {IT_FORECAST_2025['salary_predictions']['blockchain_dev']}
        """.strip()
