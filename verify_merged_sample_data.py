#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Verification script for merged SAMPLE_JOBS data
"""

from sample_data import SAMPLE_JOBS, JOB_MARKET_INSIGHTS_2024, SALARY_RANGES

def verify_merged_data():
    """Verify the merged SAMPLE_JOBS structure"""
    print("🔍 Verifying Merged Sample Data")
    print("=" * 50)
    
    print(f"\n📊 SAMPLE_JOBS Summary:")
    print(f"   Total jobs: {len(SAMPLE_JOBS)}")
    
    print(f"\n📋 All Jobs in SAMPLE_JOBS:")
    for i, job in enumerate(SAMPLE_JOBS, 1):
        hot_tag = job.get('hot_tag', '')
        benefits_count = len(job.get('benefits', []))
        print(f"   {i}. {job['id']}: {job['title']}")
        print(f"      💰 Salary: {job['salary_range']}")
        print(f"      📍 Location: {job['location']}")
        print(f"      🔧 Skills: {', '.join(job['skills'][:3])}...")
        if hot_tag:
            print(f"      🔥 Hot Tag: {hot_tag}")
        if benefits_count > 0:
            print(f"      🎁 Benefits: {benefits_count} items")
        print()
    
    # Check for enhanced features
    enhanced_jobs = [job for job in SAMPLE_JOBS if 'hot_tag' in job]
    jobs_with_benefits = [job for job in SAMPLE_JOBS if 'benefits' in job]
    
    print(f"📈 Enhanced Features:")
    print(f"   Jobs with hot_tag: {len(enhanced_jobs)}")
    print(f"   Jobs with benefits: {len(jobs_with_benefits)}")
    
    # Verify no duplicate UPDATED_SAMPLE_JOBS_2024
    try:
        from sample_data import UPDATED_SAMPLE_JOBS_2024
        print(f"   ⚠️ WARNING: UPDATED_SAMPLE_JOBS_2024 still exists!")
        print(f"   Should be removed after merge.")
    except ImportError:
        print(f"   ✅ UPDATED_SAMPLE_JOBS_2024 successfully removed")
    
    # Check skills diversity
    all_skills = set()
    for job in SAMPLE_JOBS:
        all_skills.update(job['skills'])
    
    print(f"\n🔧 Skills Analysis:")
    print(f"   Total unique skills: {len(all_skills)}")
    print(f"   Top skills: {', '.join(list(all_skills)[:10])}")
    
    # Check salary ranges
    salary_ranges = [job['salary_range'] for job in SAMPLE_JOBS]
    print(f"\n💰 Salary Range Analysis:")
    for i, salary in enumerate(salary_ranges, 1):
        print(f"   Job {i}: {salary}")
    
    print(f"\n✅ Verification Complete!")
    print(f"   Merged SAMPLE_JOBS contains {len(SAMPLE_JOBS)} jobs")
    print(f"   Enhanced with hot tags and benefits")
    print(f"   Ready for Step 5a feedback system")

if __name__ == "__main__":
    verify_merged_data()
