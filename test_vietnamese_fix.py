#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify the Vietnamese query fix
"""

import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_vietnamese_query_fix():
    """Test that the Vietnamese query now returns 'search_jobs' instead of 'other'"""
    
    print("🇻🇳 Testing Vietnamese Query Fix")
    print("=" * 50)
    
    try:
        from vector_intent_detector import VectorIntentDetector
        from vector_chatbot.vector_chatbot_router import VectorChatbotRouter
        
        # Initialize components
        print("🚀 Initializing components...")
        vector_detector = VectorIntentDetector()
        vector_router = VectorChatbotRouter()
        print("✅ Components initialized successfully!\n")
        
        # The original problematic query
        test_query = "có vị trí nào phù hợp với tôi ở Fois không nhỉ?"
        
        print(f"🎯 Testing Original Problematic Query:")
        print(f"📝 Query: '{test_query}'")
        print(f"🎯 Expected: 'search_jobs'")
        print("-" * 50)
        
        # Test 1: Vector Intent Detector
        print("🔍 Test 1: Vector Intent Detector")
        vector_intent, confidence, similarities = vector_detector.get_best_intent(test_query)
        
        print(f"   Result: {vector_intent}")
        print(f"   Confidence: {confidence:.4f}")
        
        # Show top 3 similarities
        sorted_sims = sorted(similarities.items(), key=lambda x: x[1], reverse=True)[:3]
        print(f"   Top 3 similarities:")
        for i, (intent, sim) in enumerate(sorted_sims, 1):
            marker = "👑" if i == 1 else "🔸"
            print(f"      {marker} {intent}: {sim:.4f}")
        
        detector_success = vector_intent == "search_jobs"
        print(f"   Status: {'✅ FIXED' if detector_success else '❌ STILL BROKEN'}")
        
        # Test 2: Vector Router
        print(f"\n🎨 Test 2: Vector Router Processing")
        router_result = vector_router.process_user_input(test_query, "test_user", [])
        
        mapped_intent = router_result.get('mapped_intent', 'unknown')
        router_confidence = router_result.get('confidence', 0)
        
        print(f"   Vector Intent: {router_result.get('vector_intent', 'unknown')}")
        print(f"   Mapped Intent: {mapped_intent}")
        print(f"   Confidence: {router_confidence:.4f}")
        print(f"   Needs Feedback: {router_result.get('needs_feedback', False)}")
        
        router_success = mapped_intent == "search_jobs"
        print(f"   Status: {'✅ FIXED' if router_success else '❌ STILL BROKEN'}")
        
        # Test 3: Additional Vietnamese queries
        print(f"\n🧪 Test 3: Additional Vietnamese Job Queries")
        
        additional_queries = [
            "có việc làm nào phù hợp với tôi không?",
            "vị trí nào phù hợp với CV của tôi?",
            "Fois có job nào cho tôi không?",
            "công ty có vị trí nào phù hợp không?"
        ]
        
        additional_success = 0
        for i, query in enumerate(additional_queries, 1):
            intent, conf, _ = vector_detector.get_best_intent(query)
            success = intent == "search_jobs"
            status = "✅" if success else "❌"
            
            print(f"   {i}. {status} '{query}' → {intent} ({conf:.3f})")
            if success:
                additional_success += 1
        
        additional_accuracy = (additional_success / len(additional_queries)) * 100
        print(f"   Additional queries accuracy: {additional_success}/{len(additional_queries)} ({additional_accuracy:.1f}%)")
        
        # Overall assessment
        print(f"\n" + "=" * 50)
        print("🎯 OVERALL ASSESSMENT:")
        
        overall_success = detector_success and router_success and additional_accuracy >= 75
        
        print(f"   Original Query Fix: {'✅ FIXED' if detector_success and router_success else '❌ NOT FIXED'}")
        print(f"   Additional Queries: {'✅ GOOD' if additional_accuracy >= 75 else '❌ NEEDS WORK'}")
        print(f"   Overall Status: {'🎉 SUCCESS' if overall_success else '🔧 NEEDS MORE WORK'}")
        
        if overall_success:
            print("\n💡 What was fixed:")
            print("✅ Added specific Vietnamese phrases to search_jobs intent")
            print("✅ Lowered thresholds for job-related intents")
            print("✅ Added company-specific boosting logic")
            print("✅ Fixed case sensitivity issues (OTHER → other)")
        else:
            print("\n🔧 Still needs work:")
            if not detector_success:
                print("❌ Vector intent detector still not working for Vietnamese")
            if not router_success:
                print("❌ Vector router still not mapping correctly")
            if additional_accuracy < 75:
                print("❌ Additional Vietnamese queries need improvement")
        
        return overall_success
        
    except Exception as e:
        print(f"❌ Critical error in Vietnamese query fix test: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run the Vietnamese query fix test"""
    print(f"🕒 Test started at: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    success = test_vietnamese_query_fix()
    
    print()
    print(f"🕒 Test completed at: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if success:
        print("\n🎉 Vietnamese query fix is working!")
        print("The query 'có vị trí nào phù hợp với tôi ở Fois không nhỉ?' should now return 'search_jobs'")
    else:
        print("\n🔧 Vietnamese query fix needs more work")
        print("The query is still not being classified correctly")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
