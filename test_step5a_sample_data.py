#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify Step 5a enhanced feedback with sample_data.py integration
"""

import sys
import os
from datetime import datetime

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from vector_chatbot.vector_chatbot_router import VectorC<PERSON>botRouter
from sample_data import SAMPLE_JOBS, JOB_MARKET_INSIGHTS_2024, SALARY_RANGES

def test_step5a_with_sample_data():
    """Test Step 5a feedback responses using sample_data.py"""
    print("🧪 Testing Step 5a Enhanced Feedback with Sample Data")
    print("=" * 60)
    
    try:
        # Initialize router
        print("🚀 Initializing Vector Chatbot Router...")
        router = VectorChatbotRouter()
        
        # Test cases for feedback intents
        feedback_test_cases = [
            {
                "input": "I want to find a Python developer job",
                "expected_intent_type": "job_search",
                "description": "Job search query",
                "should_use_sample_data": True
            },
            {
                "input": "What is the salary range for AI engineers?",
                "expected_intent_type": "salary_query", 
                "description": "Salary inquiry",
                "should_use_sample_data": True
            },
            {
                "input": "Can you review my CV for a software engineer position?",
                "expected_intent_type": "cv_feedback",
                "description": "CV review request",
                "should_use_sample_data": True
            },
            {
                "input": "Tell me about job opportunities in machine learning",
                "expected_intent_type": "job_search",
                "description": "ML job opportunities",
                "should_use_sample_data": True
            }
        ]
        
        print(f"\n📋 Running {len(feedback_test_cases)} feedback test cases...")
        
        successful_tests = 0
        sample_data_usage = 0
        
        for i, test_case in enumerate(feedback_test_cases, 1):
            print(f"\n{i}. {test_case['description']}")
            print(f"   Input: '{test_case['input']}'")
            
            # Process the input through the complete system
            response_data = router.process_user_input(
                test_case['input'], 
                f"test_user_{i}", 
                []
            )
            
            # Check response data
            processing_method = response_data.get('processing_method', 'unknown')
            data_source = response_data.get('data_source', 'none')
            vector_intent = response_data.get('vector_intent', 'unknown')
            confidence = response_data.get('vector_confidence', 0.0)
            
            print(f"   Vector Intent: {vector_intent}")
            print(f"   Confidence: {confidence:.4f}")
            print(f"   Processing Method: {processing_method}")
            print(f"   Data Source: {data_source}")
            
            # Check if sample data was used
            if 'sample_data' in processing_method:
                sample_data_usage += 1
                print("   ✅ Sample data integration: SUCCESS")
                
                # Check for sample data in contextual_followup
                contextual_followup = response_data.get('contextual_followup', {})
                if contextual_followup.get('data_source'):
                    print(f"   📊 Data source: {contextual_followup['data_source']}")
                
                # Check response content
                message = response_data.get('message', '')
                if len(message) > 100:  # Rich content indicates sample data usage
                    print("   ✅ Rich content generated: SUCCESS")
                else:
                    print("   ⚠️ Content seems basic")
                
            else:
                print("   ⚠️ Sample data integration: NOT DETECTED")
            
            # Check if it's a feedback intent
            if processing_method in ['feedback_function_with_sample_data', 'feedback_function']:
                successful_tests += 1
                print("   ✅ Feedback routing: CORRECT")
            else:
                print("   ❌ Feedback routing: INCORRECT")
            
            # Show sample of response
            message = response_data.get('message', '')
            print(f"   📝 Response preview: {message[:150]}...")
        
        # Test sample data availability
        print(f"\n📊 Testing Sample Data Availability:")
        print(f"   SAMPLE_JOBS: {len(SAMPLE_JOBS)} jobs")
        print(f"   JOB_MARKET_INSIGHTS_2024: {len(JOB_MARKET_INSIGHTS_2024['most_demanded_positions'])} positions")
        print(f"   SALARY_RANGES: {len(SALARY_RANGES)} levels")
        
        # Summary
        print(f"\n📊 Test Results Summary:")
        print(f"   Total tests: {len(feedback_test_cases)}")
        print(f"   Successful feedback routing: {successful_tests}/{len(feedback_test_cases)} ({successful_tests/len(feedback_test_cases)*100:.1f}%)")
        print(f"   Sample data integration: {sample_data_usage}/{len(feedback_test_cases)} ({sample_data_usage/len(feedback_test_cases)*100:.1f}%)")
        
        if successful_tests >= len(feedback_test_cases) * 0.8 and sample_data_usage >= len(feedback_test_cases) * 0.5:
            print("   🎉 Overall result: PASSED")
            print("   ✅ Step 5a enhanced feedback with sample_data.py is working!")
        else:
            print("   ❌ Overall result: NEEDS IMPROVEMENT")
            if sample_data_usage < len(feedback_test_cases) * 0.5:
                print("   ⚠️ Sample data integration needs improvement")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_sample_data_content():
    """Test the content and structure of sample_data.py"""
    print("\n🔍 Testing Sample Data Content Structure")
    print("=" * 40)
    
    try:
        # Test SAMPLE_JOBS
        print("📋 SAMPLE_JOBS:")
        for i, job in enumerate(SAMPLE_JOBS[:3], 1):
            print(f"   {i}. {job['title']} - {job['salary_range']}")
            print(f"      Skills: {', '.join(job['skills'][:3])}")
        
        # Test JOB_MARKET_INSIGHTS_2024
        print("\n📈 JOB_MARKET_INSIGHTS_2024:")
        for i, pos in enumerate(JOB_MARKET_INSIGHTS_2024['most_demanded_positions'][:3], 1):
            print(f"   {i}. {pos['title']} - {pos['avg_salary']} ({pos['demand_level']})")
        
        # Test SALARY_RANGES
        print("\n💰 SALARY_RANGES:")
        for level, data in list(SALARY_RANGES.items())[:3]:
            print(f"   {level}: {data['range']} - {data['description']}")
        
        print("\n✅ Sample data structure is valid and rich!")
        
    except Exception as e:
        print(f"❌ Error testing sample data: {e}")

if __name__ == "__main__":
    print("🚀 Starting Step 5a Enhanced Feedback Tests")
    print("=" * 60)
    
    # Test sample data content first
    test_sample_data_content()
    
    # Test the enhanced Step 5a
    success = test_step5a_with_sample_data()
    
    if success:
        print("\n🎉 Step 5a Enhanced Feedback Tests COMPLETED!")
        print("✅ Sample data integration is working correctly!")
    else:
        print("\n❌ Step 5a Enhanced Feedback Tests FAILED!")
        print("⚠️ Check the implementation and try again.")
    
    print("=" * 60)
